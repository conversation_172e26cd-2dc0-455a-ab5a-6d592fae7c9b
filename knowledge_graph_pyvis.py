import json
import networkx as nx
from pyvis.network import Network
import tkinter as tk
from tkinter import filedialog, messagebox, simpledialog
from collections import defaultdict
import webbrowser
import os
import tempfile

class InteractiveKnowledgeGraph:
    def __init__(self, json_data):
        self.data = json_data
        self.G = nx.DiGraph()
        self.visible_nodes = set()
        self.visible_edges = set()
        self.selected_node = None
        
        # Définir les couleurs pour chaque type de nœud
        self.node_type_colors = {
            'Organization': '#FF6B6B',
            'Person': '#4ECDC4',
            'Concept': '#45B7D1',
            'Document': '#96CEB4',
            'Product': '#FFEAA7',
            'Date': '#DDA0DD'
        }
        
        # Définir les couleurs pour chaque type de relation
        self.edge_type_colors = {
            'PUBLISHES': '#FF6B6B',
            'AUTHORED_BY': '#4ECDC4',
            'PUBLISHED_ON': '#45B7D1',
            'HAS_CATEGORY': '#96CEB4',
            'ABOUT': '#FFEAA7',
            'RELATED_TO': '#DDA0DD',
            'DEVELOPED_BY': '#FFB6C1',
            'IS_A_TYPE_OF': '#98FB98',
            'CREATED_BY': '#F0E68C',
            'COMMENTED_ON': '#DEB887'
        }
        
        self.build_graph()
    
    def build_graph(self):
        """Construit le graphe NetworkX à partir des données JSON"""
        # Ajouter les nœuds
        for node in self.data['nodes']:
            self.G.add_node(
                node['id'],
                label=node['label'],
                properties=node['properties']
            )
        
        # Ajouter les arêtes
        for edge in self.data['relationships']:
            self.G.add_edge(
                edge['source'],
                edge['target'],
                type=edge['type'],
                properties=edge.get('properties', {})
            )
        
        # Initialiser tous les nœuds et arêtes comme visibles
        self.visible_nodes = set(self.G.nodes())
        self.visible_edges = set(self.G.edges())
    
    def get_node_color(self, node_id):
        """Retourne la couleur d'un nœud basée sur son type"""
        node_data = self.G.nodes[node_id]
        node_type = node_data['label']
        return self.node_type_colors.get(node_type, '#CCCCCC')
    
    def get_edge_color(self, edge):
        """Retourne la couleur d'une arête basée sur son type"""
        edge_data = self.G.edges[edge]
        edge_type = edge_data['type']
        return self.edge_type_colors.get(edge_type, '#CCCCCC')
    
    def filter_by_node_type(self, node_types):
        """Filtre les nœuds par type"""
        self.visible_nodes = {
            node_id for node_id in self.G.nodes()
            if self.G.nodes[node_id]['label'] in node_types
        }
        
        # Mettre à jour les arêtes visibles
        self.visible_edges = {
            edge for edge in self.G.edges()
            if edge[0] in self.visible_nodes and edge[1] in self.visible_nodes
        }
    
    def filter_by_edge_type(self, edge_types):
        """Filtre les arêtes par type"""
        filtered_edges = {
            edge for edge in self.G.edges()
            if self.G.edges[edge]['type'] in edge_types
        }
        self.visible_edges = filtered_edges.intersection(self.visible_edges)
    
    def search_nodes(self, query):
        """Recherche des nœuds par nom ou propriétés"""
        if not query:
            return set(self.G.nodes())
        
        matching_nodes = set()
        query_lower = query.lower()
        
        for node_id in self.G.nodes():
            node_data = self.G.nodes[node_id]
            for key, value in node_data['properties'].items():
                if isinstance(value, str) and query_lower in value.lower():
                    matching_nodes.add(node_id)
                    break
            if query_lower in node_id.lower():
                matching_nodes.add(node_id)
        return matching_nodes
    
    def get_node_info(self, node_id):
        """Retourne les informations détaillées d'un nœud"""
        if node_id not in self.G.nodes():
            return None
        
        node_data = self.G.nodes[node_id]
        info = f"ID: {node_id}\\n"
        info += f"Type: {node_data['label']}\\n"
        info += "Propriétés:\\n"
        for key, value in node_data['properties'].items():
            info += f"  {key}: {value}\\n"
        
        incoming = list(self.G.predecessors(node_id))
        outgoing = list(self.G.successors(node_id))
        
        if incoming:
            info += f"\\nConnexions entrantes: {len(incoming)}\\n"
            for pred in incoming[:5]:
                edge_type = self.G.edges[pred, node_id]['type']
                info += f"  ← {pred} ({edge_type})\\n"
        
        if outgoing:
            info += f"\\nConnexions sortantes: {len(outgoing)}\\n"
            for succ in outgoing[:5]:
                edge_type = self.G.edges[node_id, succ]['type']
                info += f"  → {succ} ({edge_type})\\n"
        
        return info
    
    def create_pyvis_network(self):
        """Crée et retourne un réseau Pyvis"""
        # Créer le réseau Pyvis
        net = Network(
            height="800px",
            width="100%",
            bgcolor="#ffffff",
            font_color="black",
            directed=True
        )
        
        # Configuration des options de physique pour une meilleure interactivité
        net.set_options("""
        var options = {
          "physics": {
            "enabled": true,
            "stabilization": {"enabled": true, "iterations": 100},
            "barnesHut": {
              "gravitationalConstant": -8000,
              "centralGravity": 0.3,
              "springLength": 95,
              "springConstant": 0.04,
              "damping": 0.09
            }
          },
          "nodes": {
            "font": {"size": 12}
          },
          "edges": {
            "font": {"size": 10},
            "arrows": {"to": {"enabled": true, "scaleFactor": 0.5}}
          },
          "interaction": {
            "hover": true,
            "tooltipDelay": 200,
            "dragNodes": true,
            "dragView": true,
            "zoomView": true
          }
        }
        """)
        
        # Ajouter les nœuds visibles
        for node_id in self.visible_nodes:
            if node_id in self.G.nodes():
                node_data = self.G.nodes[node_id]
                node_color = self.get_node_color(node_id)
                
                # Préparer le nom à afficher
                name = node_data['properties'].get('name', node_id)
                if len(name) > 25:
                    display_name = name[:22] + '...'
                else:
                    display_name = name
                
                # Préparer le tooltip avec les informations détaillées
                tooltip = self.get_node_info(node_id)
                
                # Calculer la taille basée sur le degré
                degree = self.G.degree(node_id)
                #size = max(20, min(50, degree * 5))
                size = degree * 10  

                net.add_node(
                    node_id,
                    label=display_name,
                    color=node_color,
                    size=size,
                    title=tooltip,
                    group=node_data['label']
                )
        
        # Ajouter les arêtes visibles
        for edge in self.visible_edges:
            if edge[0] in self.visible_nodes and edge[1] in self.visible_nodes:
                edge_data = self.G.edges[edge]
                edge_color = self.get_edge_color(edge)
                edge_type = edge_data['type']
                
                net.add_edge(
                    edge[0],
                    edge[1],
                    label=edge_type,
                    color=edge_color,
                    title=f"Relation: {edge_type}"
                )
        
        return net
    
    def show_interactive_graph(self, output_file="knowledge_graph.html"):
        """Affiche le graphe interactif avec Pyvis"""
        net = self.create_pyvis_network()
        
        # Sauvegarder le fichier HTML
        net.save_graph(output_file)
        
        # Ouvrir le fichier dans le navigateur
        file_path = os.path.abspath(output_file)
        webbrowser.open(f'file://{file_path}')
        
        print(f"Graphe interactif sauvegardé dans: {output_file}")
        print("Le graphe s'ouvre automatiquement dans votre navigateur.")
        
        return output_file
    
    def show_statistics(self):
        """Affiche les statistiques du graphe"""
        print("=== Statistiques du Knowledge Graph ===")
        print(f"Nombre total de nœuds: {len(self.G.nodes())}")
        print(f"Nombre total d'arêtes: {len(self.G.edges())}")
        print(f"Nœuds visibles: {len(self.visible_nodes)}")
        print(f"Arêtes visibles: {len(self.visible_edges)}")
        
        # Répartition par type de nœud
        node_types = defaultdict(int)
        for node_id in self.G.nodes():
            node_type = self.G.nodes[node_id]['label']
            node_types[node_type] += 1
        
        print("\nRépartition par type de nœud:")
        for node_type, count in sorted(node_types.items()):
            print(f"  {node_type}: {count}")
        
        # Répartition par type d'arête
        edge_types = defaultdict(int)
        for edge in self.G.edges():
            edge_type = self.G.edges[edge]['type']
            edge_types[edge_type] += 1
        
        print("\nRépartition par type d'arête:")
        for edge_type, count in sorted(edge_types.items()):
            print(f"  {edge_type}: {count}")
        
        # Nœuds les plus connectés
        degrees = [(node, self.G.degree(node)) for node in self.G.nodes()]
        degrees.sort(key=lambda x: x[1], reverse=True)
        
        print("\nNœuds les plus connectés:")
        for node, degree in degrees[:10]:
            name = self.G.nodes[node]['properties'].get('name', node)
            print(f"  {name}: {degree} connexions")

def ask_json_file():
    """Demande à l'utilisateur de choisir un fichier JSON via une boite de dialogue"""
    root = tk.Tk()
    root.withdraw()
    file_path = filedialog.askopenfilename(
        title="Sélectionnez un fichier JSON",
        filetypes=(("JSON files", "*.json"), ("All files", "*.*"))
    )
    return file_path

def interactive_menu(kg):
    """Menu interactif pour filtrer et explorer le graphe"""
    root = tk.Tk()
    root.withdraw()
    
    while True:
        choice = simpledialog.askstring(
            "Menu interactif",
            "Choisissez une option:\n"
            "1. Afficher le graphe complet\n"
            "2. Filtrer par type de nœud\n"
            "3. Filtrer par type de relation\n"
            "4. Rechercher des nœuds\n"
            "5. Afficher les statistiques\n"
            "6. Quitter\n\n"
            "Entrez votre choix (1-6):"
        )
        
        if not choice or choice == '6':
            break
        
        if choice == '1':
            # Réinitialiser tous les filtres
            kg.visible_nodes = set(kg.G.nodes())
            kg.visible_edges = set(kg.G.edges())
            kg.show_interactive_graph()
        
        elif choice == '2':
            # Filtrer par type de nœud
            available_types = list(kg.node_type_colors.keys())
            types_str = ", ".join(available_types)
            selected_types = simpledialog.askstring(
                "Filtrage par type de nœud",
                f"Types disponibles: {types_str}\n\n"
                "Entrez les types à afficher (séparés par des virgules):"
            )
            
            if selected_types:
                types_list = [t.strip() for t in selected_types.split(',')]
                kg.filter_by_node_type(types_list)
                kg.show_interactive_graph()
        
        elif choice == '3':
            # Filtrer par type de relation
            available_types = list(kg.edge_type_colors.keys())
            types_str = ", ".join(available_types)
            selected_types = simpledialog.askstring(
                "Filtrage par type de relation",
                f"Types disponibles: {types_str}\n\n"
                "Entrez les types à afficher (séparés par des virgules):"
            )
            
            if selected_types:
                types_list = [t.strip() for t in selected_types.split(',')]
                kg.filter_by_edge_type(types_list)
                kg.show_interactive_graph()
        
        elif choice == '4':
            # Rechercher des nœuds
            query = simpledialog.askstring(
                "Recherche de nœuds",
                "Entrez votre terme de recherche:"
            )
            
            if query:
                matching_nodes = kg.search_nodes(query)
                if matching_nodes:
                    kg.visible_nodes = matching_nodes
                    kg.visible_edges = {
                        edge for edge in kg.G.edges()
                        if edge[0] in kg.visible_nodes and edge[1] in kg.visible_nodes
                    }
                    kg.show_interactive_graph()
                    messagebox.showinfo("Résultats", f"Trouvé {len(matching_nodes)} nœuds correspondants")
                else:
                    messagebox.showwarning("Aucun résultat", "Aucun nœud trouvé pour cette recherche")
        
        elif choice == '5':
            kg.show_statistics()

def main():
    """Fonction principale"""
    print("=== Knowledge Graph Interactif avec Pyvis ===")
    
    # Sélectionner le fichier JSON à charger
    json_file = ask_json_file()
    if not json_file:
        print("Aucun fichier JSON sélectionné.")
        return
    
    try:
        with open(json_file, "r", encoding="utf-8") as f:
            json_data = json.load(f)
    except Exception as e:
        print(f"Erreur lors du chargement du fichier JSON: {e}")
        return
    
    # Créer le graphe de connaissances
    kg = InteractiveKnowledgeGraph(json_data)
    
    # Afficher les statistiques
    kg.show_statistics()
    
    print("\n=== Instructions d'utilisation ===")
    print("- Le graphe s'ouvre automatiquement dans votre navigateur")
    print("- Utilisez la souris pour zoomer, déplacer la vue et les nœuds")
    print("- Survolez les nœuds pour voir leurs informations détaillées")
    print("- Cliquez et faites glisser les nœuds pour les repositionner")
    print("- Utilisez le menu interactif pour filtrer le graphe")
    
    # Afficher le graphe initial
    kg.show_interactive_graph()
    
    # Lancer le menu interactif
    interactive_menu(kg)
    
    return kg

if __name__ == "__main__":
    kg = main()