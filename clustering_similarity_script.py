import pandas as pd
from sentence_transformers import SentenceTransformer, util
import warnings

warnings.filterwarnings("ignore")


class ContentClusteringAnalyzer:
    def __init__(self, model_name="all-MiniLM-L6-v2"):
        """
        Initialise l'analyseur de clustering de contenu

        Args:
            model_name (str): Nom du modèle Sentence Transformers à utiliser
        """
        self.model = SentenceTransformer(model_name)
        print("Modèle chargé avec succès!")

    def load_data(self, file_path, content_column, url_column="Address"):
        """
        Charge les données depuis un fichier CSV

        Args:
            file_path (str): Chemin vers le fichier CSV
            content_column (str): Nom de la colonne contenant le contenu à analyser
            url_column (str): Nom de la colonne contenant les URLs

        Returns:
            pd.DataFrame: DataFrame avec les données chargées
        """
        try:
            df = pd.read_csv(file_path)
            print(f"Données chargées: {len(df)} lignes")

            # Vérifier que les colonnes existent
            if content_column not in df.columns:
                raise ValueError(
                    f"Colonne '{content_column}' non trouvée dans le fichier"
                )
            if url_column not in df.columns:
                raise ValueError(f"Colonne '{url_column}' non trouvée dans le fichier")

            # Nettoyer les données
            df = df.dropna(subset=[content_column, url_column])
            df = df[df[content_column].str.strip() != ""]

            print(f"Données nettoyées: {len(df)} lignes")
            return df
        except Exception as e:
            print(f"Erreur lors du chargement des données: {e}")
            return None

    def calculate_embeddings(self, corpus_sentences, batch_size=256):
        """
        Calcule les embeddings pour le corpus de phrases

        Args:
            corpus_sentences (list): Liste des phrases à analyser
            batch_size (int): Taille du batch pour le traitement

        Returns:
            torch.Tensor: Embeddings calculés
        """
        print("Calcul des embeddings...")
        corpus_embeddings = self.model.encode(
            corpus_sentences,
            batch_size=batch_size,
            show_progress_bar=True,
            convert_to_tensor=True,
        )
        print(f"Embeddings calculés pour {len(corpus_sentences)} phrases")
        return corpus_embeddings

    def detect_communities(
        self, corpus_embeddings, min_community_size=2, threshold=0.75
    ):
        """
        Détecte les communautés (clusters) dans les embeddings

        Args:
            corpus_embeddings: Embeddings calculés
            min_community_size (int): Taille minimale d'une communauté
            threshold (float): Seuil de similarité pour le clustering

        Returns:
            list: Liste des clusters détectés
        """
        print(f"Détection de communautés avec seuil {threshold}...")
        clusters = util.community_detection(
            corpus_embeddings,
            min_community_size=min_community_size,
            threshold=threshold,
        )
        print(f"Nombre de clusters détectés: {len(clusters)}")
        return clusters

    def create_cluster_dataframe(self, clusters, corpus_sentences, kw_col="content"):
        """
        Crée un DataFrame associant les phrases aux clusters

        Args:
            clusters: Liste des clusters
            corpus_sentences: Liste des phrases originales
            kw_col (str): Nom de la colonne de contenu

        Returns:
            pd.DataFrame: DataFrame avec les associations cluster-phrase
        """
        corpus_sentences_list = []
        cluster_name_list = []

        for keyword, cluster in enumerate(clusters):
            for sentence_id in cluster[0:]:
                corpus_sentences_list.append(corpus_sentences[sentence_id])
                cluster_name_list.append(
                    f"Cluster {keyword + 1}, #{len(cluster)} Elements"
                )

        df_new = pd.DataFrame()
        df_new["source_h1"] = cluster_name_list
        df_new[kw_col] = corpus_sentences_list

        return df_new

    def rename_clusters_with_shortest_phrase(self, df, kw_col="content"):
        """
        Renomme les clusters en utilisant la phrase la plus courte

        Args:
            df (pd.DataFrame): DataFrame avec les clusters
            kw_col (str): Nom de la colonne de contenu

        Returns:
            pd.DataFrame: DataFrame avec les clusters renommés
        """
        print("Renommage des clusters avec la phrase la plus courte...")

        # Calculer la longueur de chaque phrase
        df["length"] = df[kw_col].astype(str).map(len)

        # Trier par longueur croissante
        df = df.sort_values(by="length", ascending=True)

        # Remplacer le nom du cluster par la phrase la plus courte
        df["source_h1"] = df.groupby("source_h1")[kw_col].transform("first")

        # Supprimer la colonne temporaire
        df = df.drop("length", axis=1)

        return df

    def merge_with_original_data(
        self, df_clustered, df_original, kw_col="content", url_col="Address"
    ):
        """
        Fusionne les données de clustering avec les données originales

        Args:
            df_clustered: DataFrame avec les informations de clustering
            df_original: DataFrame original avec les URLs
            kw_col (str): Nom de la colonne de contenu
            url_col (str): Nom de la colonne URL

        Returns:
            pd.DataFrame: DataFrame fusionné
        """
        print("Fusion des données de clustering avec les données originales...")

        # Créer une copie pour les URLs source
        df2 = df_original[[url_col, kw_col]].copy()
        df2.rename(columns={url_col: "source_url", kw_col: "source_h1"}, inplace=True)

        # Fusionner avec les données de clustering
        df_final = df_clustered.merge(
            df2.drop_duplicates("source_h1"), how="left", on="source_h1"
        )

        return df_final

    def analyze_content_similarity(
        self,
        file_path,
        content_column,
        url_column="Address",
        min_similarity=0.75,
        min_community_size=2,
    ):
        """
        Fonction principale pour analyser la similarité du contenu

        Args:
            file_path (str): Chemin vers le fichier CSV
            content_column (str): Nom de la colonne contenant le contenu
            url_column (str): Nom de la colonne contenant les URLs
            min_similarity (float): Seuil de similarité minimum
            min_community_size (int): Taille minimale d'une communauté

        Returns:
            pd.DataFrame: DataFrame final avec les résultats de clustering
        """
        # 1. Charger les données
        df_original = self.load_data(file_path, content_column, url_column)
        if df_original is None:
            return None

        # 2. Préparer le corpus
        corpus_sentences = df_original[content_column].astype(str).tolist()

        # 3. Calculer les embeddings
        corpus_embeddings = self.calculate_embeddings(corpus_sentences)

        # 4. Détecter les communautés
        clusters = self.detect_communities(
            corpus_embeddings,
            min_community_size=min_community_size,
            threshold=min_similarity,
        )

        # 5. Créer le DataFrame des clusters
        df_clustered = self.create_cluster_dataframe(
            clusters, corpus_sentences, content_column
        )

        # 6. Renommer les clusters
        df_clustered = self.rename_clusters_with_shortest_phrase(
            df_clustered, content_column
        )

        # 7. Fusionner avec les données originales
        df_final = self.merge_with_original_data(
            df_clustered, df_original, content_column, url_column
        )

        return df_final

    def export_results(self, df_final, output_file="clustering_results.csv"):
        """
        Exporte les résultats vers un fichier CSV

        Args:
            df_final (pd.DataFrame): DataFrame avec les résultats
            output_file (str): Nom du fichier de sortie
        """
        if df_final is not None:
            df_final.to_csv(output_file, index=False)
            print(f"Résultats exportés vers {output_file}")
        else:
            print("Aucun résultat à exporter")

    def get_cluster_statistics(self, df_final, content_column="content"):
        """
        Génère des statistiques sur les clusters

        Args:
            df_final (pd.DataFrame): DataFrame avec les résultats
            content_column (str): Nom de la colonne de contenu

        Returns:
            pd.DataFrame: Statistiques des clusters
        """
        if df_final is None:
            return None

        stats = (
            df_final.groupby("source_h1")
            .agg({content_column: "count", "source_url": "nunique"})
            .rename(
                columns={
                    content_column: "nombre_phrases",
                    "source_url": "nombre_urls_uniques",
                }
            )
            .reset_index()
        )

        stats = stats.sort_values("nombre_phrases", ascending=False)

        print("\n=== STATISTIQUES DES CLUSTERS ===")
        print(f"Nombre total de clusters: {len(stats)}")
        print(f"Cluster le plus grand: {stats['nombre_phrases'].max()} phrases")
        print(f"Cluster le plus petit: {stats['nombre_phrases'].min()} phrases")
        print(f"Moyenne de phrases par cluster: {stats['nombre_phrases'].mean():.2f}")

        return stats


def main():
    # Le modèle paraphrase-multilingual-mpnet-base-v2 est généralement le plus performant pour le français car il utilise l'architecture MPNet (plus avancée que BERT)
    # et a été spécifiquement entraîné sur des données multilingues incluant le français avec un focus sur la paraphrase et la similarité sémantique.
    analyzer = ContentClusteringAnalyzer("paraphrase-multilingual-mpnet-base-v2")

    # Analyser la similarité du contenu
    # Remplacez 'your_file.csv' par le chemin vers votre fichier
    # et 'your_content_column' par le nom de votre colonne de contenu
    df_results = analyzer.analyze_content_similarity(
        file_path="your_file.csv",
        content_column="your_content_column",
        url_column="Address",
        min_similarity=0.75,
        min_community_size=2,
    )

    if df_results is not None:
        # Afficher les statistiques
        stats = analyzer.get_cluster_statistics(df_results, "your_content_column")
        print(stats.head(10))

        # Exporter les résultats
        analyzer.export_results(df_results, "clustering_results.csv")

        # Afficher un aperçu des résultats
        print("\n=== APERÇU DES RÉSULTATS ===")
        print(df_results.head())


if __name__ == "__main__":
    main()
