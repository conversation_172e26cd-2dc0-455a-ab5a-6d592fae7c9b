{"nodes": [{"id": "zonetuto", "label": "Organization", "properties": {"name": "ZoneTuto", "description": "Website focused on tutorials and technology news"}}, {"id": "mrtuto", "label": "Person", "properties": {"name": "Mr<PERSON><PERSON>", "description": "Primary author on ZoneTuto"}}, {"id": "concepteure", "label": "Person", "properties": {"name": "ConceptEure", "description": "Author on ZoneTuto"}}, {"id": "intelligence_artificielle", "label": "Concept", "properties": {"name": "Intelligence Artificielle (IA)", "type": "Technology Domain"}}, {"id": "doc_lexique_ia", "label": "Document", "properties": {"name": "Lexique pour comprendre le vocabulaire de l’intelligence artificielle", "type": "Article", "url": "https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/"}}, {"id": "vocabulaire_ia", "label": "Concept", "properties": {"name": "Vocabulaire de l'intelligence artificielle", "type": "Glossary/Lexicon"}}, {"id": "date_2025_06_12", "label": "Date", "properties": {"date": "2025-06-12"}}, {"id": "doc_gta5_bourse", "label": "Document", "properties": {"name": "Comment devenir riche dans GTA 5 avec la bourse", "type": "Article", "url": "https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/"}}, {"id": "gta_5", "label": "Product", "properties": {"name": "GTA 5", "type": "Video Game"}}, {"id": "bourse_gta", "label": "Concept", "properties": {"name": "<PERSON><PERSON><PERSON>", "context": "In GTA 5"}}, {"id": "gta_4", "label": "Product", "properties": {"name": "GTA 4", "type": "Video Game"}}, {"id": "jeux_video_category", "label": "Concept", "properties": {"name": "<PERSON><PERSON> vidéo", "type": "Category"}}, {"id": "doc_winget_windows", "label": "Document", "properties": {"name": "Le gestionnaire de paquets WinGet sous Windows pour gérer les applications", "type": "Article", "url": "https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/"}}, {"id": "winget", "label": "Product", "properties": {"name": "WinGet", "type": "Package Manager"}}, {"id": "windows", "label": "Product", "properties": {"name": "Windows", "type": "Operating System"}}, {"id": "microsoft", "label": "Organization", "properties": {"name": "Microsoft", "type": "Software Company"}}, {"id": "gestionnaire_paquets", "label": "Concept", "properties": {"name": "Gestionnaire de paquets", "type": "Software Concept"}}, {"id": "date_2025_06_11", "label": "Date", "properties": {"date": "2025-06-11"}}, {"id": "windows_category", "label": "Concept", "properties": {"name": "Windows", "type": "Category"}}, {"id": "doc_ollama_contexte", "label": "Document", "properties": {"name": "Augmenter la taille de la fenêtre de contexte dans Ollama", "type": "Article", "url": "https://zonetuto.fr/intelligence-artificielle/augmenter-la-taille-de-la-fenetre-de-contexte-dans-ollama/"}}, {"id": "ollama", "label": "Product", "properties": {"name": "Ollama", "type": "Tool for LLMs"}}, {"id": "fenetre_contexte", "label": "Concept", "properties": {"name": "Fenêtre de contexte", "context": "In Ollama"}}, {"id": "date_2025_06_01", "label": "Date", "properties": {"date": "2025-06-01"}}, {"id": "doc_windows_fichiers_longs", "label": "Document", "properties": {"name": "Windows : impossible d’ouvrir des fichiers avec un nom trop long", "type": "Article", "url": "https://zonetuto.fr/windows/impossible-ouvrir-fichiers-avec-un-nom-trop-long/"}}, {"id": "microsoft_word", "label": "Product", "properties": {"name": "Microsoft Word", "type": "Software"}}, {"id": "fichiers", "label": "Concept", "properties": {"name": "Fichiers", "description": "Digital files"}}, {"id": "noms_trop_longs", "label": "Concept", "properties": {"name": "Noms trop longs", "description": "Issue with file names"}}, {"id": "dsi", "label": "Organization", "properties": {"name": "DSI", "description": "IT Department (implied)"}}, {"id": "date_2025_05_22", "label": "Date", "properties": {"date": "2025-05-22"}}, {"id": "doc_stack_overflow", "label": "Document", "properties": {"name": "Stack Overflow est-il en train de tirer sa révérence ?", "type": "Article", "url": "https://zonetuto.fr/outils/stack-overflow-tirer-reverence/"}}, {"id": "stack_overflow", "label": "Product", "properties": {"name": "Stack Overflow", "type": "Online Platform"}}, {"id": "outils_category", "label": "Concept", "properties": {"name": "Outils", "type": "Category"}}, {"id": "date_2025_05_18", "label": "Date", "properties": {"date": "2025-05-18"}}, {"id": "doc_lister_fichiers_windows", "label": "Document", "properties": {"name": "Lister les fichiers et leur chemin complet sur un lecteur Windows", "type": "Article", "url": "https://zonetuto.fr/windows/lister-les-fichiers-et-leur-chemin-complet-sur-un-lecteur-windows/"}}, {"id": "chemin_complet", "label": "Concept", "properties": {"name": "Chemin complet", "description": "Full path of files"}}, {"id": "doc_ollama_llm_local", "label": "Document", "properties": {"name": "Ajouter et exécuter des LLM avec Ollama pour utiliser l’IA en local", "type": "Article", "url": "https://zonetuto.fr/intelligence-artificielle/ajouter-et-executer-des-llm-avec-ollama-pour-utiliser-lia-en-local/"}}, {"id": "llm", "label": "Concept", "properties": {"name": "Grands <PERSON><PERSON><PERSON><PERSON> (LLM)", "type": "AI Models"}}, {"id": "chatgpt", "label": "Product", "properties": {"name": "ChatGPT", "type": "LLM"}}, {"id": "claude", "label": "Product", "properties": {"name": "<PERSON>", "type": "LLM"}}, {"id": "anthropic", "label": "Organization", "properties": {"name": "Anthropic", "description": "Company behind Claude <PERSON>"}}, {"id": "mistral", "label": "Product", "properties": {"name": "<PERSON><PERSON><PERSON>", "type": "LLM"}}, {"id": "api_cloud", "label": "Concept", "properties": {"name": "API cloud", "description": "Cloud-based APIs for services"}}, {"id": "date_2025_05_16", "label": "Date", "properties": {"date": "2025-05-16"}}, {"id": "doc_lecteurs_reseaux_windows", "label": "Document", "properties": {"name": "Lister les lecteurs réseaux et vérifier leur espace disque dans Windows", "type": "Article", "url": "https://zonetuto.fr/windows/lister-les-lecteurs-reseaux-mappes-et-verifier-leur-espace-disque/"}}, {"id": "lecteur<PERSON>_<PERSON>seau", "label": "Concept", "properties": {"name": "Lect<PERSON><PERSON><PERSON>", "description": "Network drives"}}, {"id": "espace_disque", "label": "Concept", "properties": {"name": "Espace disque", "description": "Disk space"}}, {"id": "date_2025_05_06", "label": "Date", "properties": {"date": "2025-05-06"}}, {"id": "doc_modeles_ia_images", "label": "Document", "properties": {"name": "Classement des meilleurs modèles IA pour créer des images", "type": "Article", "url": "https://zonetuto.fr/intelligence-artificielle/classement-des-meilleurs-modeles-ia-pour-creer-des-images/"}}, {"id": "modeles_ia_images", "label": "Concept", "properties": {"name": "Modèles IA pour créer des images", "type": "AI Models"}}, {"id": "date_2025_04_24", "label": "Date", "properties": {"date": "2025-04-24"}}, {"id": "generatepress", "label": "Organization", "properties": {"name": "GeneratePress", "type": "WordPress Theme/Framework"}}, {"id": "bebert", "label": "Person", "properties": {"name": "<PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>"}}, {"id": "doc_firefox_snap_ubuntu", "label": "Document", "properties": {"name": "Firefox Snap : comment faire la mise à jour du navigateur sur Ubuntu", "type": "Article (implied from comment)"}}, {"id": "firefox_snap", "label": "Product", "properties": {"name": "Firefox Snap", "type": "Browser Software"}}, {"id": "ubuntu", "label": "Product", "properties": {"name": "Ubuntu", "type": "Operating System (Linux Distribution)"}}, {"id": "bonaventure_claude", "label": "Person", "properties": {"name": "Bonaventure Claude", "type": "<PERSON><PERSON><PERSON>"}}, {"id": "doc_filament_pla_petg", "label": "Document", "properties": {"name": "Filament PLA vs PETG : quelle différence ? Qui est le meilleur ?", "type": "Article (implied from comment)"}}, {"id": "filament_pla", "label": "Concept", "properties": {"name": "Filament PLA", "type": "3D Printing Material"}}, {"id": "filament_petg", "label": "Concept", "properties": {"name": "Filament PETG", "type": "3D Printing Material"}}, {"id": "<PERSON><PERSON>", "label": "Person", "properties": {"name": "<PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>"}}, {"id": "doc_wordpress_permissions", "label": "Document", "properties": {"name": "Ajouter les bonnes permissions sur les fichiers et dossiers de WordPress", "type": "Article (implied from comment)"}}, {"id": "wordpress", "label": "Product", "properties": {"name": "WordPress", "type": "CMS"}}, {"id": "permissions", "label": "Concept", "properties": {"name": "Permissions", "description": "File and folder permissions"}}, {"id": "jak", "label": "Person", "properties": {"name": "<PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>"}}, {"id": "doc_ecran_noir_ubuntu", "label": "Document", "properties": {"name": "Écran noir sur Ubuntu 24.04 LTS avec une carte graphique Nvidia", "type": "Article (implied from comment)"}}, {"id": "nvidia_gpu", "label": "Product", "properties": {"name": "Carte graphique Nvidia", "type": "Hardware Component"}}, {"id": "nvidia", "label": "Organization", "properties": {"name": "Nvidia", "description": "Graphics card manufacturer"}}, {"id": "ecran_noir", "label": "Concept", "properties": {"name": "Écran noir", "description": "Display issue"}}, {"id": "marc", "label": "Person", "properties": {"name": "<PERSON>", "type": "<PERSON><PERSON><PERSON>"}}, {"id": "doc_deploy_gitlab_ubuntu", "label": "Document", "properties": {"name": "Déployer un projet hébergé dans Gitlab sur Ubuntu avec clé SSH et Git", "type": "Article (implied from comment)"}}, {"id": "gitlab", "label": "Organization", "properties": {"name": "Gitlab", "type": "DevOps Platform"}}, {"id": "cle_ssh", "label": "Concept", "properties": {"name": "Clé SSH", "type": "Authentication Method"}}, {"id": "git", "label": "Concept", "properties": {"name": "Git", "type": "Version Control System"}}, {"id": "linux_category", "label": "Concept", "properties": {"name": "Linux", "type": "Category"}}], "relationships": [{"id": "rel_1", "type": "PUBLISHES", "source": "zonetuto", "target": "doc_lexique_ia", "properties": {}}, {"id": "rel_2", "type": "AUTHORED_BY", "source": "doc_lexique_ia", "target": "mrtuto", "properties": {}}, {"id": "rel_3", "type": "PUBLISHED_ON", "source": "doc_lexique_ia", "target": "date_2025_06_12", "properties": {}}, {"id": "rel_4", "type": "HAS_CATEGORY", "source": "doc_lexique_ia", "target": "intelligence_artificielle", "properties": {}}, {"id": "rel_5", "type": "ABOUT", "source": "doc_lexique_ia", "target": "vocabulaire_ia", "properties": {}}, {"id": "rel_6", "type": "PUBLISHES", "source": "zonetuto", "target": "doc_gta5_bourse", "properties": {}}, {"id": "rel_7", "type": "AUTHORED_BY", "source": "doc_gta5_bourse", "target": "mrtuto", "properties": {}}, {"id": "rel_8", "type": "PUBLISHED_ON", "source": "doc_gta5_bourse", "target": "date_2025_06_12", "properties": {}}, {"id": "rel_9", "type": "HAS_CATEGORY", "source": "doc_gta5_bourse", "target": "jeux_video_category", "properties": {}}, {"id": "rel_10", "type": "ABOUT", "source": "doc_gta5_bourse", "target": "gta_5", "properties": {}}, {"id": "rel_11", "type": "ABOUT", "source": "doc_gta5_bourse", "target": "bourse_gta", "properties": {}}, {"id": "rel_12", "type": "RELATED_TO", "source": "doc_gta5_bourse", "target": "gta_4", "properties": {"context": "previous article"}}, {"id": "rel_13", "type": "PUBLISHES", "source": "zonetuto", "target": "doc_winget_windows", "properties": {}}, {"id": "rel_14", "type": "AUTHORED_BY", "source": "doc_winget_windows", "target": "mrtuto", "properties": {}}, {"id": "rel_15", "type": "PUBLISHED_ON", "source": "doc_winget_windows", "target": "date_2025_06_12", "properties": {}}, {"id": "rel_16", "type": "PUBLISHED_ON", "source": "doc_winget_windows", "target": "date_2025_06_11", "properties": {}}, {"id": "rel_17", "type": "HAS_CATEGORY", "source": "doc_winget_windows", "target": "windows_category", "properties": {}}, {"id": "rel_18", "type": "ABOUT", "source": "doc_winget_windows", "target": "winget", "properties": {}}, {"id": "rel_19", "type": "ABOUT", "source": "doc_winget_windows", "target": "windows", "properties": {}}, {"id": "rel_20", "type": "ABOUT", "source": "doc_winget_windows", "target": "gestionnaire_paquets", "properties": {}}, {"id": "rel_21", "type": "DEVELOPED_BY", "source": "winget", "target": "microsoft", "properties": {}}, {"id": "rel_22", "type": "PUBLISHES", "source": "zonetuto", "target": "doc_ollama_contexte", "properties": {}}, {"id": "rel_23", "type": "AUTHORED_BY", "source": "doc_ollama_contexte", "target": "mrtuto", "properties": {}}, {"id": "rel_24", "type": "PUBLISHED_ON", "source": "doc_ollama_contexte", "target": "date_2025_06_01", "properties": {}}, {"id": "rel_25", "type": "HAS_CATEGORY", "source": "doc_ollama_contexte", "target": "intelligence_artificielle", "properties": {}}, {"id": "rel_26", "type": "ABOUT", "source": "doc_ollama_contexte", "target": "ollama", "properties": {}}, {"id": "rel_27", "type": "ABOUT", "source": "doc_ollama_contexte", "target": "fenetre_contexte", "properties": {}}, {"id": "rel_28", "type": "PUBLISHES", "source": "zonetuto", "target": "doc_windows_fichiers_longs", "properties": {}}, {"id": "rel_29", "type": "AUTHORED_BY", "source": "doc_windows_fichiers_longs", "target": "mrtuto", "properties": {}}, {"id": "rel_30", "type": "PUBLISHED_ON", "source": "doc_windows_fichiers_longs", "target": "date_2025_05_22", "properties": {}}, {"id": "rel_31", "type": "HAS_CATEGORY", "source": "doc_windows_fichiers_longs", "target": "windows_category", "properties": {}}, {"id": "rel_32", "type": "ABOUT", "source": "doc_windows_fichiers_longs", "target": "windows", "properties": {}}, {"id": "rel_33", "type": "ABOUT", "source": "doc_windows_fichiers_longs", "target": "fichiers", "properties": {}}, {"id": "rel_34", "type": "ABOUT", "source": "doc_windows_fichiers_longs", "target": "noms_trop_longs", "properties": {}}, {"id": "rel_35", "type": "RELATED_TO", "source": "microsoft_word", "target": "fichiers", "properties": {}}, {"id": "rel_36", "type": "RELATES_TO", "source": "dsi", "target": "noms_trop_longs", "properties": {"context": "users making jokes to DSI"}}, {"id": "rel_37", "type": "PUBLISHES", "source": "zonetuto", "target": "doc_stack_overflow", "properties": {}}, {"id": "rel_38", "type": "AUTHORED_BY", "source": "doc_stack_overflow", "target": "concepteure", "properties": {}}, {"id": "rel_39", "type": "PUBLISHED_ON", "source": "doc_stack_overflow", "target": "date_2025_05_18", "properties": {}}, {"id": "rel_40", "type": "HAS_CATEGORY", "source": "doc_stack_overflow", "target": "intelligence_artificielle", "properties": {}}, {"id": "rel_41", "type": "HAS_CATEGORY", "source": "doc_stack_overflow", "target": "outils_category", "properties": {}}, {"id": "rel_42", "type": "ABOUT", "source": "doc_stack_overflow", "target": "stack_overflow", "properties": {}}, {"id": "rel_43", "type": "PUBLISHES", "source": "zonetuto", "target": "doc_lister_fichiers_windows", "properties": {}}, {"id": "rel_44", "type": "AUTHORED_BY", "source": "doc_lister_fichiers_windows", "target": "mrtuto", "properties": {}}, {"id": "rel_45", "type": "PUBLISHED_ON", "source": "doc_lister_fichiers_windows", "target": "date_2025_05_18", "properties": {}}, {"id": "rel_46", "type": "HAS_CATEGORY", "source": "doc_lister_fichiers_windows", "target": "windows_category", "properties": {}}, {"id": "rel_47", "type": "ABOUT", "source": "doc_lister_fichiers_windows", "target": "fichiers", "properties": {}}, {"id": "rel_48", "type": "ABOUT", "source": "doc_lister_fichiers_windows", "target": "chemin_complet", "properties": {}}, {"id": "rel_49", "type": "ABOUT", "source": "doc_lister_fichiers_windows", "target": "windows", "properties": {}}, {"id": "rel_50", "type": "PUBLISHES", "source": "zonetuto", "target": "doc_ollama_llm_local", "properties": {}}, {"id": "rel_51", "type": "AUTHORED_BY", "source": "doc_ollama_llm_local", "target": "mrtuto", "properties": {}}, {"id": "rel_52", "type": "PUBLISHED_ON", "source": "doc_ollama_llm_local", "target": "date_2025_05_16", "properties": {}}, {"id": "rel_53", "type": "HAS_CATEGORY", "source": "doc_ollama_llm_local", "target": "intelligence_artificielle", "properties": {}}, {"id": "rel_54", "type": "ABOUT", "source": "doc_ollama_llm_local", "target": "ollama", "properties": {}}, {"id": "rel_55", "type": "ABOUT", "source": "doc_ollama_llm_local", "target": "llm", "properties": {}}, {"id": "rel_56", "type": "IS_A_TYPE_OF", "source": "chatgpt", "target": "llm", "properties": {}}, {"id": "rel_57", "type": "IS_A_TYPE_OF", "source": "claude", "target": "llm", "properties": {}}, {"id": "rel_58", "type": "CREATED_BY", "source": "claude", "target": "anthropic", "properties": {}}, {"id": "rel_59", "type": "IS_A_TYPE_OF", "source": "mistral", "target": "llm", "properties": {}}, {"id": "rel_60", "type": "RELATED_TO", "source": "api_cloud", "target": "llm", "properties": {"context": "barrier to local use"}}, {"id": "rel_61", "type": "PUBLISHES", "source": "zonetuto", "target": "doc_lecteurs_reseaux_windows", "properties": {}}, {"id": "rel_62", "type": "AUTHORED_BY", "source": "doc_lecteurs_reseaux_windows", "target": "mrtuto", "properties": {}}, {"id": "rel_63", "type": "PUBLISHED_ON", "source": "doc_lecteurs_reseaux_windows", "target": "date_2025_05_22", "properties": {}}, {"id": "rel_64", "type": "PUBLISHED_ON", "source": "doc_lecteurs_reseaux_windows", "target": "date_2025_05_06", "properties": {}}, {"id": "rel_65", "type": "HAS_CATEGORY", "source": "doc_lecteurs_reseaux_windows", "target": "windows_category", "properties": {}}, {"id": "rel_66", "type": "ABOUT", "source": "doc_lecteurs_reseaux_windows", "target": "lecteur<PERSON>_<PERSON>seau", "properties": {}}, {"id": "rel_67", "type": "ABOUT", "source": "doc_lecteurs_reseaux_windows", "target": "espace_disque", "properties": {}}, {"id": "rel_68", "type": "ABOUT", "source": "doc_lecteurs_reseaux_windows", "target": "windows", "properties": {}}, {"id": "rel_69", "type": "PUBLISHES", "source": "zonetuto", "target": "doc_modeles_ia_images", "properties": {}}, {"id": "rel_70", "type": "AUTHORED_BY", "source": "doc_modeles_ia_images", "target": "mrtuto", "properties": {}}, {"id": "rel_71", "type": "PUBLISHED_ON", "source": "doc_modeles_ia_images", "target": "date_2025_04_24", "properties": {}}, {"id": "rel_72", "type": "HAS_CATEGORY", "source": "doc_modeles_ia_images", "target": "intelligence_artificielle", "properties": {}}, {"id": "rel_73", "type": "ABOUT", "source": "doc_modeles_ia_images", "target": "modeles_ia_images", "properties": {}}, {"id": "rel_74", "type": "BUILT_WITH", "source": "zonetuto", "target": "generatepress", "properties": {}}, {"id": "rel_75", "type": "COMMENTED_ON", "source": "bebert", "target": "doc_firefox_snap_ubuntu", "properties": {}}, {"id": "rel_76", "type": "RELATED_TO", "source": "firefox_snap", "target": "ubuntu", "properties": {"context": "browser on OS"}}, {"id": "rel_77", "type": "HAS_CATEGORY", "source": "doc_firefox_snap_ubuntu", "target": "linux_category", "properties": {}}, {"id": "rel_78", "type": "COMMENTED_ON", "source": "bonaventure_claude", "target": "doc_filament_pla_petg", "properties": {}}, {"id": "rel_79", "type": "COMPARES", "source": "doc_filament_pla_petg", "target": "filament_pla", "properties": {}}, {"id": "rel_80", "type": "COMPARES", "source": "doc_filament_pla_petg", "target": "filament_petg", "properties": {}}, {"id": "rel_81", "type": "COMMENTED_ON", "source": "<PERSON><PERSON>", "target": "doc_wordpress_permissions", "properties": {}}, {"id": "rel_82", "type": "ABOUT", "source": "doc_wordpress_permissions", "target": "wordpress", "properties": {}}, {"id": "rel_83", "type": "ABOUT", "source": "doc_wordpress_permissions", "target": "permissions", "properties": {}}, {"id": "rel_84", "type": "COMMENTED_ON", "source": "jak", "target": "doc_ecran_noir_ubuntu", "properties": {}}, {"id": "rel_85", "type": "ABOUT", "source": "doc_ecran_noir_ubuntu", "target": "ubuntu", "properties": {}}, {"id": "rel_86", "type": "ABOUT", "source": "doc_ecran_noir_ubuntu", "target": "nvidia_gpu", "properties": {}}, {"id": "rel_87", "type": "ABOUT", "source": "doc_ecran_noir_ubuntu", "target": "ecran_noir", "properties": {}}, {"id": "rel_88", "type": "PRODUCES", "source": "nvidia", "target": "nvidia_gpu", "properties": {}}, {"id": "rel_89", "type": "COMMENTED_ON", "source": "marc", "target": "doc_deploy_gitlab_ubuntu", "properties": {}}, {"id": "rel_90", "type": "ABOUT", "source": "doc_deploy_gitlab_ubuntu", "target": "gitlab", "properties": {}}, {"id": "rel_91", "type": "ABOUT", "source": "doc_deploy_gitlab_ubuntu", "target": "ubuntu", "properties": {}}, {"id": "rel_92", "type": "ABOUT", "source": "doc_deploy_gitlab_ubuntu", "target": "cle_ssh", "properties": {}}, {"id": "rel_93", "type": "ABOUT", "source": "doc_deploy_gitlab_ubuntu", "target": "git", "properties": {}}]}