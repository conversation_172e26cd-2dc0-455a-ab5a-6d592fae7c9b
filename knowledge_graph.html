<html>
    <head>
        <meta charset="utf-8">
        
            <script src="lib/bindings/utils.js"></script>
            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/dist/vis-network.min.css" integrity="sha512-WgxfT5LWjfszlPHXRmBWHkV2eceiWTOBvrKCNbdgDYTHrT2AeLCGbF4sZlZw3UMN3WtL0tGUoIAKsu8mllg/XA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
            <script src="https://cdnjs.cloudflare.com/ajax/libs/vis-network/9.1.2/dist/vis-network.min.js" integrity="sha512-LnvoEWDFrqGHlHmDD2101OrLcbsfkrzoSpvtSQtxK3RMnRV0eOkhhBN2dXHKRrUU8p2DGRTk35n4O8nWSVe1mQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
            
        
<center>
<h1></h1>
</center>

<!-- <link rel="stylesheet" href="../node_modules/vis/dist/vis.min.css" type="text/css" />
<script type="text/javascript" src="../node_modules/vis/dist/vis.js"> </script>-->
        <link
          href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/css/bootstrap.min.css"
          rel="stylesheet"
          integrity="sha384-eOJMYsd53ii+scO/bJGFsiCZc+5NDVN2yr8+0RDqr0Ql0h+rP48ckxlpbzKgwra6"
          crossorigin="anonymous"
        />
        <script
          src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.0-beta3/dist/js/bootstrap.bundle.min.js"
          integrity="sha384-JEW9xMcG8R+pH31jmWH6WWP0WintQrMb4s7ZOdauHnUtxwoG2vI5DkLtS3qm9Ekf"
          crossorigin="anonymous"
        ></script>


        <center>
          <h1></h1>
        </center>
        <style type="text/css">

             #mynetwork {
                 width: 100%;
                 height: 800px;
                 background-color: #ffffff;
                 border: 1px solid lightgray;
                 position: relative;
                 float: left;
             }

             

             

             
        </style>
    </head>


    <body>
        <div class="card" style="width: 100%">
            
            
            <div id="mynetwork" class="card-body"></div>
        </div>

        
        

        <script type="text/javascript">

              // initialize global variables.
              var edges;
              var nodes;
              var allNodes;
              var allEdges;
              var nodeColors;
              var originalNodes;
              var network;
              var container;
              var options, data;
              var filter = {
                  item : '',
                  property : '',
                  value : []
              };

              

              

              // This method is responsible for drawing the graph, returns the drawn network
              function drawGraph() {
                  var container = document.getElementById('mynetwork');

                  

                  // parsing and collecting nodes and edges from the python
                  nodes = new vis.DataSet([{"font": {"color": "black"}, "group": "Person", "id": "jak", "label": "Jak", "shape": "dot", "size": 10, "title": "ID: jak\\nType: Person\\nPropri\u00e9t\u00e9s:\\n  name: Jak\\n  type: Commenter\\n\\nConnexions sortantes: 1\\n  \u2192 doc_ecran_noir_ubuntu (COMMENTED_ON)\\n"}, {"font": {"color": "black"}, "group": "Document", "id": "doc_wordpress_permissions", "label": "Ajouter les bonnes per...", "shape": "dot", "size": 30, "title": "ID: doc_wordpress_permissions\\nType: Document\\nPropri\u00e9t\u00e9s:\\n  name: Ajouter les bonnes permissions sur les fichiers et dossiers de WordPress\\n  type: Article (implied from comment)\\n\\nConnexions entrantes: 1\\n  \u2190 mathieu (COMMENTED_ON)\\n\\nConnexions sortantes: 2\\n  \u2192 wordpress (ABOUT)\\n  \u2192 permissions (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Concept", "id": "linux_category", "label": "Linux", "shape": "dot", "size": 10, "title": "ID: linux_category\\nType: Concept\\nPropri\u00e9t\u00e9s:\\n  name: Linux\\n  type: Category\\n\\nConnexions entrantes: 1\\n  \u2190 doc_firefox_snap_ubuntu (HAS_CATEGORY)\\n"}, {"font": {"color": "black"}, "group": "Organization", "id": "anthropic", "label": "Anthropic", "shape": "dot", "size": 10, "title": "ID: anthropic\\nType: Organization\\nPropri\u00e9t\u00e9s:\\n  name: Anthropic\\n  description: Company behind Claude LLM\\n\\nConnexions entrantes: 1\\n  \u2190 claude (CREATED_BY)\\n"}, {"font": {"color": "black"}, "group": "Document", "id": "doc_firefox_snap_ubuntu", "label": "Firefox Snap : comment...", "shape": "dot", "size": 20, "title": "ID: doc_firefox_snap_ubuntu\\nType: Document\\nPropri\u00e9t\u00e9s:\\n  name: Firefox Snap : comment faire la mise \u00e0 jour du navigateur sur Ubuntu\\n  type: Article (implied from comment)\\n\\nConnexions entrantes: 1\\n  \u2190 bebert (COMMENTED_ON)\\n\\nConnexions sortantes: 1\\n  \u2192 linux_category (HAS_CATEGORY)\\n"}, {"font": {"color": "black"}, "group": "Product", "id": "nvidia_gpu", "label": "Carte graphique Nvidia", "shape": "dot", "size": 20, "title": "ID: nvidia_gpu\\nType: Product\\nPropri\u00e9t\u00e9s:\\n  name: Carte graphique Nvidia\\n  type: Hardware Component\\n\\nConnexions entrantes: 2\\n  \u2190 doc_ecran_noir_ubuntu (ABOUT)\\n  \u2190 nvidia (PRODUCES)\\n"}, {"font": {"color": "black"}, "group": "Person", "id": "concepteure", "label": "ConceptEure", "shape": "dot", "size": 10, "title": "ID: concepteure\\nType: Person\\nPropri\u00e9t\u00e9s:\\n  name: ConceptEure\\n  description: Author on ZoneTuto\\n\\nConnexions entrantes: 1\\n  \u2190 doc_stack_overflow (AUTHORED_BY)\\n"}, {"font": {"color": "black"}, "group": "Date", "id": "date_2025_06_11", "label": "date_2025_06_11", "shape": "dot", "size": 10, "title": "ID: date_2025_06_11\\nType: Date\\nPropri\u00e9t\u00e9s:\\n  date: 2025-06-11\\n\\nConnexions entrantes: 1\\n  \u2190 doc_winget_windows (PUBLISHED_ON)\\n"}, {"font": {"color": "black"}, "group": "Product", "id": "microsoft_word", "label": "Microsoft Word", "shape": "dot", "size": 10, "title": "ID: microsoft_word\\nType: Product\\nPropri\u00e9t\u00e9s:\\n  name: Microsoft Word\\n  type: Software\\n\\nConnexions sortantes: 1\\n  \u2192 fichiers (RELATED_TO)\\n"}, {"font": {"color": "black"}, "group": "Product", "id": "claude", "label": "Claude", "shape": "dot", "size": 20, "title": "ID: claude\\nType: Product\\nPropri\u00e9t\u00e9s:\\n  name: Claude\\n  type: LLM\\n\\nConnexions sortantes: 2\\n  \u2192 llm (IS_A_TYPE_OF)\\n  \u2192 anthropic (CREATED_BY)\\n"}, {"font": {"color": "black"}, "group": "Organization", "id": "nvidia", "label": "Nvidia", "shape": "dot", "size": 10, "title": "ID: nvidia\\nType: Organization\\nPropri\u00e9t\u00e9s:\\n  name: Nvidia\\n  description: Graphics card manufacturer\\n\\nConnexions sortantes: 1\\n  \u2192 nvidia_gpu (PRODUCES)\\n"}, {"font": {"color": "black"}, "group": "Concept", "id": "vocabulaire_ia", "label": "Vocabulaire de l\u0027intel...", "shape": "dot", "size": 10, "title": "ID: vocabulaire_ia\\nType: Concept\\nPropri\u00e9t\u00e9s:\\n  name: Vocabulaire de l\u0027intelligence artificielle\\n  type: Glossary/Lexicon\\n\\nConnexions entrantes: 1\\n  \u2190 doc_lexique_ia (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Concept", "id": "permissions", "label": "Permissions", "shape": "dot", "size": 10, "title": "ID: permissions\\nType: Concept\\nPropri\u00e9t\u00e9s:\\n  name: Permissions\\n  description: File and folder permissions\\n\\nConnexions entrantes: 1\\n  \u2190 doc_wordpress_permissions (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Product", "id": "stack_overflow", "label": "Stack Overflow", "shape": "dot", "size": 10, "title": "ID: stack_overflow\\nType: Product\\nPropri\u00e9t\u00e9s:\\n  name: Stack Overflow\\n  type: Online Platform\\n\\nConnexions entrantes: 1\\n  \u2190 doc_stack_overflow (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Product", "id": "wordpress", "label": "WordPress", "shape": "dot", "size": 10, "title": "ID: wordpress\\nType: Product\\nPropri\u00e9t\u00e9s:\\n  name: WordPress\\n  type: CMS\\n\\nConnexions entrantes: 1\\n  \u2190 doc_wordpress_permissions (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Concept", "id": "fichiers", "label": "Fichiers", "shape": "dot", "size": 30, "title": "ID: fichiers\\nType: Concept\\nPropri\u00e9t\u00e9s:\\n  name: Fichiers\\n  description: Digital files\\n\\nConnexions entrantes: 3\\n  \u2190 doc_windows_fichiers_longs (ABOUT)\\n  \u2190 microsoft_word (RELATED_TO)\\n  \u2190 doc_lister_fichiers_windows (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Concept", "id": "filament_petg", "label": "Filament PETG", "shape": "dot", "size": 10, "title": "ID: filament_petg\\nType: Concept\\nPropri\u00e9t\u00e9s:\\n  name: Filament PETG\\n  type: 3D Printing Material\\n\\nConnexions entrantes: 1\\n  \u2190 doc_filament_pla_petg (COMPARES)\\n"}, {"font": {"color": "black"}, "group": "Concept", "id": "windows_category", "label": "Windows", "shape": "dot", "size": 40, "title": "ID: windows_category\\nType: Concept\\nPropri\u00e9t\u00e9s:\\n  name: Windows\\n  type: Category\\n\\nConnexions entrantes: 4\\n  \u2190 doc_winget_windows (HAS_CATEGORY)\\n  \u2190 doc_windows_fichiers_longs (HAS_CATEGORY)\\n  \u2190 doc_lister_fichiers_windows (HAS_CATEGORY)\\n  \u2190 doc_lecteurs_reseaux_windows (HAS_CATEGORY)\\n"}, {"font": {"color": "black"}, "group": "Organization", "id": "gitlab", "label": "Gitlab", "shape": "dot", "size": 10, "title": "ID: gitlab\\nType: Organization\\nPropri\u00e9t\u00e9s:\\n  name: Gitlab\\n  type: DevOps Platform\\n\\nConnexions entrantes: 1\\n  \u2190 doc_deploy_gitlab_ubuntu (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Person", "id": "mrtuto", "label": "MrTuto", "shape": "dot", "size": 90, "title": "ID: mrtuto\\nType: Person\\nPropri\u00e9t\u00e9s:\\n  name: MrTuto\\n  description: Primary author on ZoneTuto\\n\\nConnexions entrantes: 9\\n  \u2190 doc_lexique_ia (AUTHORED_BY)\\n  \u2190 doc_gta5_bourse (AUTHORED_BY)\\n  \u2190 doc_winget_windows (AUTHORED_BY)\\n  \u2190 doc_ollama_contexte (AUTHORED_BY)\\n  \u2190 doc_windows_fichiers_longs (AUTHORED_BY)\\n"}, {"font": {"color": "black"}, "group": "Product", "id": "windows", "label": "Windows", "shape": "dot", "size": 40, "title": "ID: windows\\nType: Product\\nPropri\u00e9t\u00e9s:\\n  name: Windows\\n  type: Operating System\\n\\nConnexions entrantes: 4\\n  \u2190 doc_winget_windows (ABOUT)\\n  \u2190 doc_windows_fichiers_longs (ABOUT)\\n  \u2190 doc_lister_fichiers_windows (ABOUT)\\n  \u2190 doc_lecteurs_reseaux_windows (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Document", "id": "doc_deploy_gitlab_ubuntu", "label": "D\u00e9ployer un projet h\u00e9b...", "shape": "dot", "size": 50, "title": "ID: doc_deploy_gitlab_ubuntu\\nType: Document\\nPropri\u00e9t\u00e9s:\\n  name: D\u00e9ployer un projet h\u00e9berg\u00e9 dans Gitlab sur Ubuntu avec cl\u00e9 SSH et Git\\n  type: Article (implied from comment)\\n\\nConnexions entrantes: 1\\n  \u2190 marc (COMMENTED_ON)\\n\\nConnexions sortantes: 4\\n  \u2192 gitlab (ABOUT)\\n  \u2192 ubuntu (ABOUT)\\n  \u2192 cle_ssh (ABOUT)\\n  \u2192 git (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Organization", "id": "zonetuto", "label": "ZoneTuto", "shape": "dot", "size": 110, "title": "ID: zonetuto\\nType: Organization\\nPropri\u00e9t\u00e9s:\\n  name: ZoneTuto\\n  description: Website focused on tutorials and technology news\\n\\nConnexions sortantes: 11\\n  \u2192 doc_lexique_ia (PUBLISHES)\\n  \u2192 doc_gta5_bourse (PUBLISHES)\\n  \u2192 doc_winget_windows (PUBLISHES)\\n  \u2192 doc_ollama_contexte (PUBLISHES)\\n  \u2192 doc_windows_fichiers_longs (PUBLISHES)\\n"}, {"font": {"color": "black"}, "group": "Document", "id": "doc_lister_fichiers_windows", "label": "Lister les fichiers et...", "shape": "dot", "size": 70, "title": "ID: doc_lister_fichiers_windows\\nType: Document\\nPropri\u00e9t\u00e9s:\\n  name: Lister les fichiers et leur chemin complet sur un lecteur Windows\\n  type: Article\\n  url: https://zonetuto.fr/windows/lister-les-fichiers-et-leur-chemin-complet-sur-un-lecteur-windows/\\n\\nConnexions entrantes: 1\\n  \u2190 zonetuto (PUBLISHES)\\n\\nConnexions sortantes: 6\\n  \u2192 mrtuto (AUTHORED_BY)\\n  \u2192 date_2025_05_18 (PUBLISHED_ON)\\n  \u2192 windows_category (HAS_CATEGORY)\\n  \u2192 fichiers (ABOUT)\\n  \u2192 chemin_complet (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Document", "id": "doc_lexique_ia", "label": "Lexique pour comprendr...", "shape": "dot", "size": 50, "title": "ID: doc_lexique_ia\\nType: Document\\nPropri\u00e9t\u00e9s:\\n  name: Lexique pour comprendre le vocabulaire de l\u2019intelligence artificielle\\n  type: Article\\n  url: https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/\\n\\nConnexions entrantes: 1\\n  \u2190 zonetuto (PUBLISHES)\\n\\nConnexions sortantes: 4\\n  \u2192 mrtuto (AUTHORED_BY)\\n  \u2192 date_2025_06_12 (PUBLISHED_ON)\\n  \u2192 intelligence_artificielle (HAS_CATEGORY)\\n  \u2192 vocabulaire_ia (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Date", "id": "date_2025_06_01", "label": "date_2025_06_01", "shape": "dot", "size": 10, "title": "ID: date_2025_06_01\\nType: Date\\nPropri\u00e9t\u00e9s:\\n  date: 2025-06-01\\n\\nConnexions entrantes: 1\\n  \u2190 doc_ollama_contexte (PUBLISHED_ON)\\n"}, {"font": {"color": "black"}, "group": "Document", "id": "doc_lecteurs_reseaux_windows", "label": "Lister les lecteurs r\u00e9...", "shape": "dot", "size": 80, "title": "ID: doc_lecteurs_reseaux_windows\\nType: Document\\nPropri\u00e9t\u00e9s:\\n  name: Lister les lecteurs r\u00e9seaux et v\u00e9rifier leur espace disque dans Windows\\n  type: Article\\n  url: https://zonetuto.fr/windows/lister-les-lecteurs-reseaux-mappes-et-verifier-leur-espace-disque/\\n\\nConnexions entrantes: 1\\n  \u2190 zonetuto (PUBLISHES)\\n\\nConnexions sortantes: 7\\n  \u2192 mrtuto (AUTHORED_BY)\\n  \u2192 date_2025_05_22 (PUBLISHED_ON)\\n  \u2192 date_2025_05_06 (PUBLISHED_ON)\\n  \u2192 windows_category (HAS_CATEGORY)\\n  \u2192 lecteurs_reseau (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Concept", "id": "modeles_ia_images", "label": "Mod\u00e8les IA pour cr\u00e9er ...", "shape": "dot", "size": 10, "title": "ID: modeles_ia_images\\nType: Concept\\nPropri\u00e9t\u00e9s:\\n  name: Mod\u00e8les IA pour cr\u00e9er des images\\n  type: AI Models\\n\\nConnexions entrantes: 1\\n  \u2190 doc_modeles_ia_images (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Person", "id": "marc", "label": "Marc", "shape": "dot", "size": 10, "title": "ID: marc\\nType: Person\\nPropri\u00e9t\u00e9s:\\n  name: Marc\\n  type: Commenter\\n\\nConnexions sortantes: 1\\n  \u2192 doc_deploy_gitlab_ubuntu (COMMENTED_ON)\\n"}, {"font": {"color": "black"}, "group": "Concept", "id": "fenetre_contexte", "label": "Fen\u00eatre de contexte", "shape": "dot", "size": 10, "title": "ID: fenetre_contexte\\nType: Concept\\nPropri\u00e9t\u00e9s:\\n  name: Fen\u00eatre de contexte\\n  context: In Ollama\\n\\nConnexions entrantes: 1\\n  \u2190 doc_ollama_contexte (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Date", "id": "date_2025_05_16", "label": "date_2025_05_16", "shape": "dot", "size": 10, "title": "ID: date_2025_05_16\\nType: Date\\nPropri\u00e9t\u00e9s:\\n  date: 2025-05-16\\n\\nConnexions entrantes: 1\\n  \u2190 doc_ollama_llm_local (PUBLISHED_ON)\\n"}, {"font": {"color": "black"}, "group": "Document", "id": "doc_ollama_llm_local", "label": "Ajouter et ex\u00e9cuter de...", "shape": "dot", "size": 60, "title": "ID: doc_ollama_llm_local\\nType: Document\\nPropri\u00e9t\u00e9s:\\n  name: Ajouter et ex\u00e9cuter des LLM avec Ollama pour utiliser l\u2019IA en local\\n  type: Article\\n  url: https://zonetuto.fr/intelligence-artificielle/ajouter-et-executer-des-llm-avec-ollama-pour-utiliser-lia-en-local/\\n\\nConnexions entrantes: 1\\n  \u2190 zonetuto (PUBLISHES)\\n\\nConnexions sortantes: 5\\n  \u2192 mrtuto (AUTHORED_BY)\\n  \u2192 date_2025_05_16 (PUBLISHED_ON)\\n  \u2192 intelligence_artificielle (HAS_CATEGORY)\\n  \u2192 ollama (ABOUT)\\n  \u2192 llm (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Document", "id": "doc_windows_fichiers_longs", "label": "Windows : impossible d...", "shape": "dot", "size": 70, "title": "ID: doc_windows_fichiers_longs\\nType: Document\\nPropri\u00e9t\u00e9s:\\n  name: Windows : impossible d\u2019ouvrir des fichiers avec un nom trop long\\n  type: Article\\n  url: https://zonetuto.fr/windows/impossible-ouvrir-fichiers-avec-un-nom-trop-long/\\n\\nConnexions entrantes: 1\\n  \u2190 zonetuto (PUBLISHES)\\n\\nConnexions sortantes: 6\\n  \u2192 mrtuto (AUTHORED_BY)\\n  \u2192 date_2025_05_22 (PUBLISHED_ON)\\n  \u2192 windows_category (HAS_CATEGORY)\\n  \u2192 windows (ABOUT)\\n  \u2192 fichiers (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Document", "id": "doc_modeles_ia_images", "label": "Classement des meilleu...", "shape": "dot", "size": 50, "title": "ID: doc_modeles_ia_images\\nType: Document\\nPropri\u00e9t\u00e9s:\\n  name: Classement des meilleurs mod\u00e8les IA pour cr\u00e9er des images\\n  type: Article\\n  url: https://zonetuto.fr/intelligence-artificielle/classement-des-meilleurs-modeles-ia-pour-creer-des-images/\\n\\nConnexions entrantes: 1\\n  \u2190 zonetuto (PUBLISHES)\\n\\nConnexions sortantes: 4\\n  \u2192 mrtuto (AUTHORED_BY)\\n  \u2192 date_2025_04_24 (PUBLISHED_ON)\\n  \u2192 intelligence_artificielle (HAS_CATEGORY)\\n  \u2192 modeles_ia_images (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Document", "id": "doc_stack_overflow", "label": "Stack Overflow est-il ...", "shape": "dot", "size": 60, "title": "ID: doc_stack_overflow\\nType: Document\\nPropri\u00e9t\u00e9s:\\n  name: Stack Overflow est-il en train de tirer sa r\u00e9v\u00e9rence ?\\n  type: Article\\n  url: https://zonetuto.fr/outils/stack-overflow-tirer-reverence/\\n\\nConnexions entrantes: 1\\n  \u2190 zonetuto (PUBLISHES)\\n\\nConnexions sortantes: 5\\n  \u2192 concepteure (AUTHORED_BY)\\n  \u2192 date_2025_05_18 (PUBLISHED_ON)\\n  \u2192 intelligence_artificielle (HAS_CATEGORY)\\n  \u2192 outils_category (HAS_CATEGORY)\\n  \u2192 stack_overflow (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Date", "id": "date_2025_05_06", "label": "date_2025_05_06", "shape": "dot", "size": 10, "title": "ID: date_2025_05_06\\nType: Date\\nPropri\u00e9t\u00e9s:\\n  date: 2025-05-06\\n\\nConnexions entrantes: 1\\n  \u2190 doc_lecteurs_reseaux_windows (PUBLISHED_ON)\\n"}, {"font": {"color": "black"}, "group": "Product", "id": "gta_5", "label": "GTA 5", "shape": "dot", "size": 10, "title": "ID: gta_5\\nType: Product\\nPropri\u00e9t\u00e9s:\\n  name: GTA 5\\n  type: Video Game\\n\\nConnexions entrantes: 1\\n  \u2190 doc_gta5_bourse (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Concept", "id": "espace_disque", "label": "Espace disque", "shape": "dot", "size": 10, "title": "ID: espace_disque\\nType: Concept\\nPropri\u00e9t\u00e9s:\\n  name: Espace disque\\n  description: Disk space\\n\\nConnexions entrantes: 1\\n  \u2190 doc_lecteurs_reseaux_windows (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Date", "id": "date_2025_04_24", "label": "date_2025_04_24", "shape": "dot", "size": 10, "title": "ID: date_2025_04_24\\nType: Date\\nPropri\u00e9t\u00e9s:\\n  date: 2025-04-24\\n\\nConnexions entrantes: 1\\n  \u2190 doc_modeles_ia_images (PUBLISHED_ON)\\n"}, {"font": {"color": "black"}, "group": "Product", "id": "chatgpt", "label": "ChatGPT", "shape": "dot", "size": 10, "title": "ID: chatgpt\\nType: Product\\nPropri\u00e9t\u00e9s:\\n  name: ChatGPT\\n  type: LLM\\n\\nConnexions sortantes: 1\\n  \u2192 llm (IS_A_TYPE_OF)\\n"}, {"font": {"color": "black"}, "group": "Product", "id": "ubuntu", "label": "Ubuntu", "shape": "dot", "size": 30, "title": "ID: ubuntu\\nType: Product\\nPropri\u00e9t\u00e9s:\\n  name: Ubuntu\\n  type: Operating System (Linux Distribution)\\n\\nConnexions entrantes: 3\\n  \u2190 firefox_snap (RELATED_TO)\\n  \u2190 doc_ecran_noir_ubuntu (ABOUT)\\n  \u2190 doc_deploy_gitlab_ubuntu (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Organization", "id": "dsi", "label": "DSI", "shape": "dot", "size": 10, "title": "ID: dsi\\nType: Organization\\nPropri\u00e9t\u00e9s:\\n  name: DSI\\n  description: IT Department (implied)\\n\\nConnexions sortantes: 1\\n  \u2192 noms_trop_longs (RELATES_TO)\\n"}, {"font": {"color": "black"}, "group": "Concept", "id": "chemin_complet", "label": "Chemin complet", "shape": "dot", "size": 10, "title": "ID: chemin_complet\\nType: Concept\\nPropri\u00e9t\u00e9s:\\n  name: Chemin complet\\n  description: Full path of files\\n\\nConnexions entrantes: 1\\n  \u2190 doc_lister_fichiers_windows (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Concept", "id": "cle_ssh", "label": "Cl\u00e9 SSH", "shape": "dot", "size": 10, "title": "ID: cle_ssh\\nType: Concept\\nPropri\u00e9t\u00e9s:\\n  name: Cl\u00e9 SSH\\n  type: Authentication Method\\n\\nConnexions entrantes: 1\\n  \u2190 doc_deploy_gitlab_ubuntu (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Organization", "id": "generatepress", "label": "GeneratePress", "shape": "dot", "size": 10, "title": "ID: generatepress\\nType: Organization\\nPropri\u00e9t\u00e9s:\\n  name: GeneratePress\\n  type: WordPress Theme/Framework\\n\\nConnexions entrantes: 1\\n  \u2190 zonetuto (BUILT_WITH)\\n"}, {"font": {"color": "black"}, "group": "Product", "id": "winget", "label": "WinGet", "shape": "dot", "size": 20, "title": "ID: winget\\nType: Product\\nPropri\u00e9t\u00e9s:\\n  name: WinGet\\n  type: Package Manager\\n\\nConnexions entrantes: 1\\n  \u2190 doc_winget_windows (ABOUT)\\n\\nConnexions sortantes: 1\\n  \u2192 microsoft (DEVELOPED_BY)\\n"}, {"font": {"color": "black"}, "group": "Product", "id": "gta_4", "label": "GTA 4", "shape": "dot", "size": 10, "title": "ID: gta_4\\nType: Product\\nPropri\u00e9t\u00e9s:\\n  name: GTA 4\\n  type: Video Game\\n\\nConnexions entrantes: 1\\n  \u2190 doc_gta5_bourse (RELATED_TO)\\n"}, {"font": {"color": "black"}, "group": "Document", "id": "doc_gta5_bourse", "label": "Comment devenir riche ...", "shape": "dot", "size": 70, "title": "ID: doc_gta5_bourse\\nType: Document\\nPropri\u00e9t\u00e9s:\\n  name: Comment devenir riche dans GTA 5 avec la bourse\\n  type: Article\\n  url: https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/\\n\\nConnexions entrantes: 1\\n  \u2190 zonetuto (PUBLISHES)\\n\\nConnexions sortantes: 6\\n  \u2192 mrtuto (AUTHORED_BY)\\n  \u2192 date_2025_06_12 (PUBLISHED_ON)\\n  \u2192 jeux_video_category (HAS_CATEGORY)\\n  \u2192 gta_5 (ABOUT)\\n  \u2192 bourse_gta (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Person", "id": "bebert", "label": "Bebert", "shape": "dot", "size": 10, "title": "ID: bebert\\nType: Person\\nPropri\u00e9t\u00e9s:\\n  name: Bebert\\n  type: Commenter\\n\\nConnexions sortantes: 1\\n  \u2192 doc_firefox_snap_ubuntu (COMMENTED_ON)\\n"}, {"font": {"color": "black"}, "group": "Document", "id": "doc_filament_pla_petg", "label": "Filament PLA vs PETG :...", "shape": "dot", "size": 30, "title": "ID: doc_filament_pla_petg\\nType: Document\\nPropri\u00e9t\u00e9s:\\n  name: Filament PLA vs PETG : quelle diff\u00e9rence ? Qui est le meilleur ?\\n  type: Article (implied from comment)\\n\\nConnexions entrantes: 1\\n  \u2190 bonaventure_claude (COMMENTED_ON)\\n\\nConnexions sortantes: 2\\n  \u2192 filament_pla (COMPARES)\\n  \u2192 filament_petg (COMPARES)\\n"}, {"font": {"color": "black"}, "group": "Concept", "id": "filament_pla", "label": "Filament PLA", "shape": "dot", "size": 10, "title": "ID: filament_pla\\nType: Concept\\nPropri\u00e9t\u00e9s:\\n  name: Filament PLA\\n  type: 3D Printing Material\\n\\nConnexions entrantes: 1\\n  \u2190 doc_filament_pla_petg (COMPARES)\\n"}, {"font": {"color": "black"}, "group": "Concept", "id": "bourse_gta", "label": "Bourse", "shape": "dot", "size": 10, "title": "ID: bourse_gta\\nType: Concept\\nPropri\u00e9t\u00e9s:\\n  name: Bourse\\n  context: In GTA 5\\n\\nConnexions entrantes: 1\\n  \u2190 doc_gta5_bourse (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Concept", "id": "noms_trop_longs", "label": "Noms trop longs", "shape": "dot", "size": 20, "title": "ID: noms_trop_longs\\nType: Concept\\nPropri\u00e9t\u00e9s:\\n  name: Noms trop longs\\n  description: Issue with file names\\n\\nConnexions entrantes: 2\\n  \u2190 doc_windows_fichiers_longs (ABOUT)\\n  \u2190 dsi (RELATES_TO)\\n"}, {"font": {"color": "black"}, "group": "Organization", "id": "microsoft", "label": "Microsoft", "shape": "dot", "size": 10, "title": "ID: microsoft\\nType: Organization\\nPropri\u00e9t\u00e9s:\\n  name: Microsoft\\n  type: Software Company\\n\\nConnexions entrantes: 1\\n  \u2190 winget (DEVELOPED_BY)\\n"}, {"font": {"color": "black"}, "group": "Concept", "id": "outils_category", "label": "Outils", "shape": "dot", "size": 10, "title": "ID: outils_category\\nType: Concept\\nPropri\u00e9t\u00e9s:\\n  name: Outils\\n  type: Category\\n\\nConnexions entrantes: 1\\n  \u2190 doc_stack_overflow (HAS_CATEGORY)\\n"}, {"font": {"color": "black"}, "group": "Concept", "id": "intelligence_artificielle", "label": "Intelligence Artificie...", "shape": "dot", "size": 50, "title": "ID: intelligence_artificielle\\nType: Concept\\nPropri\u00e9t\u00e9s:\\n  name: Intelligence Artificielle (IA)\\n  type: Technology Domain\\n\\nConnexions entrantes: 5\\n  \u2190 doc_lexique_ia (HAS_CATEGORY)\\n  \u2190 doc_ollama_contexte (HAS_CATEGORY)\\n  \u2190 doc_stack_overflow (HAS_CATEGORY)\\n  \u2190 doc_ollama_llm_local (HAS_CATEGORY)\\n  \u2190 doc_modeles_ia_images (HAS_CATEGORY)\\n"}, {"font": {"color": "black"}, "group": "Product", "id": "ollama", "label": "Ollama", "shape": "dot", "size": 20, "title": "ID: ollama\\nType: Product\\nPropri\u00e9t\u00e9s:\\n  name: Ollama\\n  type: Tool for LLMs\\n\\nConnexions entrantes: 2\\n  \u2190 doc_ollama_contexte (ABOUT)\\n  \u2190 doc_ollama_llm_local (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Date", "id": "date_2025_06_12", "label": "date_2025_06_12", "shape": "dot", "size": 30, "title": "ID: date_2025_06_12\\nType: Date\\nPropri\u00e9t\u00e9s:\\n  date: 2025-06-12\\n\\nConnexions entrantes: 3\\n  \u2190 doc_lexique_ia (PUBLISHED_ON)\\n  \u2190 doc_gta5_bourse (PUBLISHED_ON)\\n  \u2190 doc_winget_windows (PUBLISHED_ON)\\n"}, {"font": {"color": "black"}, "group": "Product", "id": "firefox_snap", "label": "Firefox Snap", "shape": "dot", "size": 10, "title": "ID: firefox_snap\\nType: Product\\nPropri\u00e9t\u00e9s:\\n  name: Firefox Snap\\n  type: Browser Software\\n\\nConnexions sortantes: 1\\n  \u2192 ubuntu (RELATED_TO)\\n"}, {"font": {"color": "black"}, "group": "Concept", "id": "lecteurs_reseau", "label": "Lecteurs r\u00e9seau", "shape": "dot", "size": 10, "title": "ID: lecteurs_reseau\\nType: Concept\\nPropri\u00e9t\u00e9s:\\n  name: Lecteurs r\u00e9seau\\n  description: Network drives\\n\\nConnexions entrantes: 1\\n  \u2190 doc_lecteurs_reseaux_windows (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Person", "id": "bonaventure_claude", "label": "Bonaventure Claude", "shape": "dot", "size": 10, "title": "ID: bonaventure_claude\\nType: Person\\nPropri\u00e9t\u00e9s:\\n  name: Bonaventure Claude\\n  type: Commenter\\n\\nConnexions sortantes: 1\\n  \u2192 doc_filament_pla_petg (COMMENTED_ON)\\n"}, {"font": {"color": "black"}, "group": "Person", "id": "mathieu", "label": "Mathieu", "shape": "dot", "size": 10, "title": "ID: mathieu\\nType: Person\\nPropri\u00e9t\u00e9s:\\n  name: Mathieu\\n  type: Commenter\\n\\nConnexions sortantes: 1\\n  \u2192 doc_wordpress_permissions (COMMENTED_ON)\\n"}, {"font": {"color": "black"}, "group": "Concept", "id": "gestionnaire_paquets", "label": "Gestionnaire de paquets", "shape": "dot", "size": 10, "title": "ID: gestionnaire_paquets\\nType: Concept\\nPropri\u00e9t\u00e9s:\\n  name: Gestionnaire de paquets\\n  type: Software Concept\\n\\nConnexions entrantes: 1\\n  \u2190 doc_winget_windows (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Date", "id": "date_2025_05_22", "label": "date_2025_05_22", "shape": "dot", "size": 20, "title": "ID: date_2025_05_22\\nType: Date\\nPropri\u00e9t\u00e9s:\\n  date: 2025-05-22\\n\\nConnexions entrantes: 2\\n  \u2190 doc_windows_fichiers_longs (PUBLISHED_ON)\\n  \u2190 doc_lecteurs_reseaux_windows (PUBLISHED_ON)\\n"}, {"font": {"color": "black"}, "group": "Product", "id": "mistral", "label": "Mistral", "shape": "dot", "size": 10, "title": "ID: mistral\\nType: Product\\nPropri\u00e9t\u00e9s:\\n  name: Mistral\\n  type: LLM\\n\\nConnexions sortantes: 1\\n  \u2192 llm (IS_A_TYPE_OF)\\n"}, {"font": {"color": "black"}, "group": "Document", "id": "doc_ecran_noir_ubuntu", "label": "\u00c9cran noir sur Ubuntu ...", "shape": "dot", "size": 40, "title": "ID: doc_ecran_noir_ubuntu\\nType: Document\\nPropri\u00e9t\u00e9s:\\n  name: \u00c9cran noir sur Ubuntu 24.04 LTS avec une carte graphique Nvidia\\n  type: Article (implied from comment)\\n\\nConnexions entrantes: 1\\n  \u2190 jak (COMMENTED_ON)\\n\\nConnexions sortantes: 3\\n  \u2192 ubuntu (ABOUT)\\n  \u2192 nvidia_gpu (ABOUT)\\n  \u2192 ecran_noir (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Document", "id": "doc_ollama_contexte", "label": "Augmenter la taille de...", "shape": "dot", "size": 60, "title": "ID: doc_ollama_contexte\\nType: Document\\nPropri\u00e9t\u00e9s:\\n  name: Augmenter la taille de la fen\u00eatre de contexte dans Ollama\\n  type: Article\\n  url: https://zonetuto.fr/intelligence-artificielle/augmenter-la-taille-de-la-fenetre-de-contexte-dans-ollama/\\n\\nConnexions entrantes: 1\\n  \u2190 zonetuto (PUBLISHES)\\n\\nConnexions sortantes: 5\\n  \u2192 mrtuto (AUTHORED_BY)\\n  \u2192 date_2025_06_01 (PUBLISHED_ON)\\n  \u2192 intelligence_artificielle (HAS_CATEGORY)\\n  \u2192 ollama (ABOUT)\\n  \u2192 fenetre_contexte (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Concept", "id": "git", "label": "Git", "shape": "dot", "size": 10, "title": "ID: git\\nType: Concept\\nPropri\u00e9t\u00e9s:\\n  name: Git\\n  type: Version Control System\\n\\nConnexions entrantes: 1\\n  \u2190 doc_deploy_gitlab_ubuntu (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Date", "id": "date_2025_05_18", "label": "date_2025_05_18", "shape": "dot", "size": 20, "title": "ID: date_2025_05_18\\nType: Date\\nPropri\u00e9t\u00e9s:\\n  date: 2025-05-18\\n\\nConnexions entrantes: 2\\n  \u2190 doc_stack_overflow (PUBLISHED_ON)\\n  \u2190 doc_lister_fichiers_windows (PUBLISHED_ON)\\n"}, {"font": {"color": "black"}, "group": "Concept", "id": "api_cloud", "label": "API cloud", "shape": "dot", "size": 10, "title": "ID: api_cloud\\nType: Concept\\nPropri\u00e9t\u00e9s:\\n  name: API cloud\\n  description: Cloud-based APIs for services\\n\\nConnexions sortantes: 1\\n  \u2192 llm (RELATED_TO)\\n"}, {"font": {"color": "black"}, "group": "Document", "id": "doc_winget_windows", "label": "Le gestionnaire de paq...", "shape": "dot", "size": 80, "title": "ID: doc_winget_windows\\nType: Document\\nPropri\u00e9t\u00e9s:\\n  name: Le gestionnaire de paquets WinGet sous Windows pour g\u00e9rer les applications\\n  type: Article\\n  url: https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/\\n\\nConnexions entrantes: 1\\n  \u2190 zonetuto (PUBLISHES)\\n\\nConnexions sortantes: 7\\n  \u2192 mrtuto (AUTHORED_BY)\\n  \u2192 date_2025_06_12 (PUBLISHED_ON)\\n  \u2192 date_2025_06_11 (PUBLISHED_ON)\\n  \u2192 windows_category (HAS_CATEGORY)\\n  \u2192 winget (ABOUT)\\n"}, {"font": {"color": "black"}, "group": "Concept", "id": "llm", "label": "Grands Mod\u00e8les de Lang...", "shape": "dot", "size": 50, "title": "ID: llm\\nType: Concept\\nPropri\u00e9t\u00e9s:\\n  name: Grands Mod\u00e8les de Langage (LLM)\\n  type: AI Models\\n\\nConnexions entrantes: 5\\n  \u2190 doc_ollama_llm_local (ABOUT)\\n  \u2190 chatgpt (IS_A_TYPE_OF)\\n  \u2190 claude (IS_A_TYPE_OF)\\n  \u2190 mistral (IS_A_TYPE_OF)\\n  \u2190 api_cloud (RELATED_TO)\\n"}, {"font": {"color": "black"}, "group": "Concept", "id": "jeux_video_category", "label": "Jeux vid\u00e9o", "shape": "dot", "size": 10, "title": "ID: jeux_video_category\\nType: Concept\\nPropri\u00e9t\u00e9s:\\n  name: Jeux vid\u00e9o\\n  type: Category\\n\\nConnexions entrantes: 1\\n  \u2190 doc_gta5_bourse (HAS_CATEGORY)\\n"}, {"font": {"color": "black"}, "group": "Concept", "id": "ecran_noir", "label": "\u00c9cran noir", "shape": "dot", "size": 10, "title": "ID: ecran_noir\\nType: Concept\\nPropri\u00e9t\u00e9s:\\n  name: \u00c9cran noir\\n  description: Display issue\\n\\nConnexions entrantes: 1\\n  \u2190 doc_ecran_noir_ubuntu (ABOUT)\\n"}]);
                  edges = new vis.DataSet([{"arrows": "to", "color": "#DEB887", "from": "jak", "label": "COMMENTED_ON", "title": "Relation: COMMENTED_ON", "to": "doc_ecran_noir_ubuntu"}, {"arrows": "to", "color": "#45B7D1", "from": "doc_lister_fichiers_windows", "label": "PUBLISHED_ON", "title": "Relation: PUBLISHED_ON", "to": "date_2025_05_18"}, {"arrows": "to", "color": "#4ECDC4", "from": "doc_ollama_contexte", "label": "AUTHORED_BY", "title": "Relation: AUTHORED_BY", "to": "mrtuto"}, {"arrows": "to", "color": "#96CEB4", "from": "doc_stack_overflow", "label": "HAS_CATEGORY", "title": "Relation: HAS_CATEGORY", "to": "intelligence_artificielle"}, {"arrows": "to", "color": "#96CEB4", "from": "doc_ollama_llm_local", "label": "HAS_CATEGORY", "title": "Relation: HAS_CATEGORY", "to": "intelligence_artificielle"}, {"arrows": "to", "color": "#4ECDC4", "from": "doc_stack_overflow", "label": "AUTHORED_BY", "title": "Relation: AUTHORED_BY", "to": "concepteure"}, {"arrows": "to", "color": "#DDA0DD", "from": "firefox_snap", "label": "RELATED_TO", "title": "Relation: RELATED_TO", "to": "ubuntu"}, {"arrows": "to", "color": "#45B7D1", "from": "doc_stack_overflow", "label": "PUBLISHED_ON", "title": "Relation: PUBLISHED_ON", "to": "date_2025_05_18"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_windows_fichiers_longs", "label": "ABOUT", "title": "Relation: ABOUT", "to": "noms_trop_longs"}, {"arrows": "to", "color": "#FF6B6B", "from": "zonetuto", "label": "PUBLISHES", "title": "Relation: PUBLISHES", "to": "doc_stack_overflow"}, {"arrows": "to", "color": "#CCCCCC", "from": "dsi", "label": "RELATES_TO", "title": "Relation: RELATES_TO", "to": "noms_trop_longs"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_windows_fichiers_longs", "label": "ABOUT", "title": "Relation: ABOUT", "to": "fichiers"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_ollama_llm_local", "label": "ABOUT", "title": "Relation: ABOUT", "to": "ollama"}, {"arrows": "to", "color": "#DDA0DD", "from": "microsoft_word", "label": "RELATED_TO", "title": "Relation: RELATED_TO", "to": "fichiers"}, {"arrows": "to", "color": "#96CEB4", "from": "doc_lister_fichiers_windows", "label": "HAS_CATEGORY", "title": "Relation: HAS_CATEGORY", "to": "windows_category"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_lecteurs_reseaux_windows", "label": "ABOUT", "title": "Relation: ABOUT", "to": "espace_disque"}, {"arrows": "to", "color": "#FF6B6B", "from": "zonetuto", "label": "PUBLISHES", "title": "Relation: PUBLISHES", "to": "doc_gta5_bourse"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_winget_windows", "label": "ABOUT", "title": "Relation: ABOUT", "to": "gestionnaire_paquets"}, {"arrows": "to", "color": "#DDA0DD", "from": "doc_gta5_bourse", "label": "RELATED_TO", "title": "Relation: RELATED_TO", "to": "gta_4"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_deploy_gitlab_ubuntu", "label": "ABOUT", "title": "Relation: ABOUT", "to": "cle_ssh"}, {"arrows": "to", "color": "#96CEB4", "from": "doc_modeles_ia_images", "label": "HAS_CATEGORY", "title": "Relation: HAS_CATEGORY", "to": "intelligence_artificielle"}, {"arrows": "to", "color": "#DEB887", "from": "mathieu", "label": "COMMENTED_ON", "title": "Relation: COMMENTED_ON", "to": "doc_wordpress_permissions"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_ecran_noir_ubuntu", "label": "ABOUT", "title": "Relation: ABOUT", "to": "ubuntu"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_ecran_noir_ubuntu", "label": "ABOUT", "title": "Relation: ABOUT", "to": "nvidia_gpu"}, {"arrows": "to", "color": "#4ECDC4", "from": "doc_lexique_ia", "label": "AUTHORED_BY", "title": "Relation: AUTHORED_BY", "to": "mrtuto"}, {"arrows": "to", "color": "#96CEB4", "from": "doc_gta5_bourse", "label": "HAS_CATEGORY", "title": "Relation: HAS_CATEGORY", "to": "jeux_video_category"}, {"arrows": "to", "color": "#FF6B6B", "from": "zonetuto", "label": "PUBLISHES", "title": "Relation: PUBLISHES", "to": "doc_windows_fichiers_longs"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_deploy_gitlab_ubuntu", "label": "ABOUT", "title": "Relation: ABOUT", "to": "ubuntu"}, {"arrows": "to", "color": "#FF6B6B", "from": "zonetuto", "label": "PUBLISHES", "title": "Relation: PUBLISHES", "to": "doc_ollama_llm_local"}, {"arrows": "to", "color": "#4ECDC4", "from": "doc_gta5_bourse", "label": "AUTHORED_BY", "title": "Relation: AUTHORED_BY", "to": "mrtuto"}, {"arrows": "to", "color": "#98FB98", "from": "chatgpt", "label": "IS_A_TYPE_OF", "title": "Relation: IS_A_TYPE_OF", "to": "llm"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_lexique_ia", "label": "ABOUT", "title": "Relation: ABOUT", "to": "vocabulaire_ia"}, {"arrows": "to", "color": "#F0E68C", "from": "claude", "label": "CREATED_BY", "title": "Relation: CREATED_BY", "to": "anthropic"}, {"arrows": "to", "color": "#96CEB4", "from": "doc_windows_fichiers_longs", "label": "HAS_CATEGORY", "title": "Relation: HAS_CATEGORY", "to": "windows_category"}, {"arrows": "to", "color": "#96CEB4", "from": "doc_stack_overflow", "label": "HAS_CATEGORY", "title": "Relation: HAS_CATEGORY", "to": "outils_category"}, {"arrows": "to", "color": "#FF6B6B", "from": "zonetuto", "label": "PUBLISHES", "title": "Relation: PUBLISHES", "to": "doc_lexique_ia"}, {"arrows": "to", "color": "#96CEB4", "from": "doc_winget_windows", "label": "HAS_CATEGORY", "title": "Relation: HAS_CATEGORY", "to": "windows_category"}, {"arrows": "to", "color": "#45B7D1", "from": "doc_lexique_ia", "label": "PUBLISHED_ON", "title": "Relation: PUBLISHED_ON", "to": "date_2025_06_12"}, {"arrows": "to", "color": "#4ECDC4", "from": "doc_lister_fichiers_windows", "label": "AUTHORED_BY", "title": "Relation: AUTHORED_BY", "to": "mrtuto"}, {"arrows": "to", "color": "#CCCCCC", "from": "nvidia", "label": "PRODUCES", "title": "Relation: PRODUCES", "to": "nvidia_gpu"}, {"arrows": "to", "color": "#4ECDC4", "from": "doc_windows_fichiers_longs", "label": "AUTHORED_BY", "title": "Relation: AUTHORED_BY", "to": "mrtuto"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_lecteurs_reseaux_windows", "label": "ABOUT", "title": "Relation: ABOUT", "to": "lecteurs_reseau"}, {"arrows": "to", "color": "#4ECDC4", "from": "doc_winget_windows", "label": "AUTHORED_BY", "title": "Relation: AUTHORED_BY", "to": "mrtuto"}, {"arrows": "to", "color": "#45B7D1", "from": "doc_modeles_ia_images", "label": "PUBLISHED_ON", "title": "Relation: PUBLISHED_ON", "to": "date_2025_04_24"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_wordpress_permissions", "label": "ABOUT", "title": "Relation: ABOUT", "to": "permissions"}, {"arrows": "to", "color": "#4ECDC4", "from": "doc_ollama_llm_local", "label": "AUTHORED_BY", "title": "Relation: AUTHORED_BY", "to": "mrtuto"}, {"arrows": "to", "color": "#45B7D1", "from": "doc_gta5_bourse", "label": "PUBLISHED_ON", "title": "Relation: PUBLISHED_ON", "to": "date_2025_06_12"}, {"arrows": "to", "color": "#45B7D1", "from": "doc_lecteurs_reseaux_windows", "label": "PUBLISHED_ON", "title": "Relation: PUBLISHED_ON", "to": "date_2025_05_22"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_ollama_contexte", "label": "ABOUT", "title": "Relation: ABOUT", "to": "fenetre_contexte"}, {"arrows": "to", "color": "#FF6B6B", "from": "zonetuto", "label": "PUBLISHES", "title": "Relation: PUBLISHES", "to": "doc_ollama_contexte"}, {"arrows": "to", "color": "#CCCCCC", "from": "doc_filament_pla_petg", "label": "COMPARES", "title": "Relation: COMPARES", "to": "filament_pla"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_lister_fichiers_windows", "label": "ABOUT", "title": "Relation: ABOUT", "to": "chemin_complet"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_modeles_ia_images", "label": "ABOUT", "title": "Relation: ABOUT", "to": "modeles_ia_images"}, {"arrows": "to", "color": "#96CEB4", "from": "doc_ollama_contexte", "label": "HAS_CATEGORY", "title": "Relation: HAS_CATEGORY", "to": "intelligence_artificielle"}, {"arrows": "to", "color": "#DDA0DD", "from": "api_cloud", "label": "RELATED_TO", "title": "Relation: RELATED_TO", "to": "llm"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_lecteurs_reseaux_windows", "label": "ABOUT", "title": "Relation: ABOUT", "to": "windows"}, {"arrows": "to", "color": "#45B7D1", "from": "doc_winget_windows", "label": "PUBLISHED_ON", "title": "Relation: PUBLISHED_ON", "to": "date_2025_06_12"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_deploy_gitlab_ubuntu", "label": "ABOUT", "title": "Relation: ABOUT", "to": "gitlab"}, {"arrows": "to", "color": "#CCCCCC", "from": "doc_filament_pla_petg", "label": "COMPARES", "title": "Relation: COMPARES", "to": "filament_petg"}, {"arrows": "to", "color": "#4ECDC4", "from": "doc_modeles_ia_images", "label": "AUTHORED_BY", "title": "Relation: AUTHORED_BY", "to": "mrtuto"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_ollama_contexte", "label": "ABOUT", "title": "Relation: ABOUT", "to": "ollama"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_gta5_bourse", "label": "ABOUT", "title": "Relation: ABOUT", "to": "bourse_gta"}, {"arrows": "to", "color": "#98FB98", "from": "mistral", "label": "IS_A_TYPE_OF", "title": "Relation: IS_A_TYPE_OF", "to": "llm"}, {"arrows": "to", "color": "#FF6B6B", "from": "zonetuto", "label": "PUBLISHES", "title": "Relation: PUBLISHES", "to": "doc_lecteurs_reseaux_windows"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_wordpress_permissions", "label": "ABOUT", "title": "Relation: ABOUT", "to": "wordpress"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_deploy_gitlab_ubuntu", "label": "ABOUT", "title": "Relation: ABOUT", "to": "git"}, {"arrows": "to", "color": "#45B7D1", "from": "doc_lecteurs_reseaux_windows", "label": "PUBLISHED_ON", "title": "Relation: PUBLISHED_ON", "to": "date_2025_05_06"}, {"arrows": "to", "color": "#45B7D1", "from": "doc_windows_fichiers_longs", "label": "PUBLISHED_ON", "title": "Relation: PUBLISHED_ON", "to": "date_2025_05_22"}, {"arrows": "to", "color": "#DEB887", "from": "bonaventure_claude", "label": "COMMENTED_ON", "title": "Relation: COMMENTED_ON", "to": "doc_filament_pla_petg"}, {"arrows": "to", "color": "#FFB6C1", "from": "winget", "label": "DEVELOPED_BY", "title": "Relation: DEVELOPED_BY", "to": "microsoft"}, {"arrows": "to", "color": "#FF6B6B", "from": "zonetuto", "label": "PUBLISHES", "title": "Relation: PUBLISHES", "to": "doc_winget_windows"}, {"arrows": "to", "color": "#45B7D1", "from": "doc_ollama_contexte", "label": "PUBLISHED_ON", "title": "Relation: PUBLISHED_ON", "to": "date_2025_06_01"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_lister_fichiers_windows", "label": "ABOUT", "title": "Relation: ABOUT", "to": "windows"}, {"arrows": "to", "color": "#96CEB4", "from": "doc_lexique_ia", "label": "HAS_CATEGORY", "title": "Relation: HAS_CATEGORY", "to": "intelligence_artificielle"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_windows_fichiers_longs", "label": "ABOUT", "title": "Relation: ABOUT", "to": "windows"}, {"arrows": "to", "color": "#96CEB4", "from": "doc_firefox_snap_ubuntu", "label": "HAS_CATEGORY", "title": "Relation: HAS_CATEGORY", "to": "linux_category"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_lister_fichiers_windows", "label": "ABOUT", "title": "Relation: ABOUT", "to": "fichiers"}, {"arrows": "to", "color": "#45B7D1", "from": "doc_winget_windows", "label": "PUBLISHED_ON", "title": "Relation: PUBLISHED_ON", "to": "date_2025_06_11"}, {"arrows": "to", "color": "#45B7D1", "from": "doc_ollama_llm_local", "label": "PUBLISHED_ON", "title": "Relation: PUBLISHED_ON", "to": "date_2025_05_16"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_winget_windows", "label": "ABOUT", "title": "Relation: ABOUT", "to": "windows"}, {"arrows": "to", "color": "#FF6B6B", "from": "zonetuto", "label": "PUBLISHES", "title": "Relation: PUBLISHES", "to": "doc_modeles_ia_images"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_gta5_bourse", "label": "ABOUT", "title": "Relation: ABOUT", "to": "gta_5"}, {"arrows": "to", "color": "#CCCCCC", "from": "zonetuto", "label": "BUILT_WITH", "title": "Relation: BUILT_WITH", "to": "generatepress"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_winget_windows", "label": "ABOUT", "title": "Relation: ABOUT", "to": "winget"}, {"arrows": "to", "color": "#DEB887", "from": "bebert", "label": "COMMENTED_ON", "title": "Relation: COMMENTED_ON", "to": "doc_firefox_snap_ubuntu"}, {"arrows": "to", "color": "#96CEB4", "from": "doc_lecteurs_reseaux_windows", "label": "HAS_CATEGORY", "title": "Relation: HAS_CATEGORY", "to": "windows_category"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_stack_overflow", "label": "ABOUT", "title": "Relation: ABOUT", "to": "stack_overflow"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_ollama_llm_local", "label": "ABOUT", "title": "Relation: ABOUT", "to": "llm"}, {"arrows": "to", "color": "#FFEAA7", "from": "doc_ecran_noir_ubuntu", "label": "ABOUT", "title": "Relation: ABOUT", "to": "ecran_noir"}, {"arrows": "to", "color": "#DEB887", "from": "marc", "label": "COMMENTED_ON", "title": "Relation: COMMENTED_ON", "to": "doc_deploy_gitlab_ubuntu"}, {"arrows": "to", "color": "#4ECDC4", "from": "doc_lecteurs_reseaux_windows", "label": "AUTHORED_BY", "title": "Relation: AUTHORED_BY", "to": "mrtuto"}, {"arrows": "to", "color": "#98FB98", "from": "claude", "label": "IS_A_TYPE_OF", "title": "Relation: IS_A_TYPE_OF", "to": "llm"}, {"arrows": "to", "color": "#FF6B6B", "from": "zonetuto", "label": "PUBLISHES", "title": "Relation: PUBLISHES", "to": "doc_lister_fichiers_windows"}]);

                  nodeColors = {};
                  allNodes = nodes.get({ returnType: "Object" });
                  for (nodeId in allNodes) {
                    nodeColors[nodeId] = allNodes[nodeId].color;
                  }
                  allEdges = edges.get({ returnType: "Object" });
                  // adding nodes and edges to the graph
                  data = {nodes: nodes, edges: edges};

                  var options = {"physics": {"enabled": true, "stabilization": {"enabled": true, "iterations": 100}, "barnesHut": {"gravitationalConstant": -8000, "centralGravity": 0.3, "springLength": 95, "springConstant": 0.04, "damping": 0.09}}, "nodes": {"font": {"size": 12}}, "edges": {"font": {"size": 10}, "arrows": {"to": {"enabled": true, "scaleFactor": 0.5}}}, "interaction": {"hover": true, "tooltipDelay": 200, "dragNodes": true, "dragView": true, "zoomView": true}};

                  


                  

                  network = new vis.Network(container, data, options);

                  

                  

                  


                  

                  return network;

              }
              drawGraph();
        </script>
    </body>
</html>