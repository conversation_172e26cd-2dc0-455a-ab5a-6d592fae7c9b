import trafilatura
import requests
import csv
import re
from typing import List


def extract_text_from_html(url: str) -> str:
    """
    Extrait le contenu texte d'une page HTML en utilisant Trafilatura.

    Args:
        url (str): L'URL de la page HTML à traiter

    Returns:
        str: Le contenu texte extrait de la page
    """
    try:
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }

        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()

        text = trafilatura.extract(response.text)

        return text if text else ""
    except requests.RequestException as e:
        print(f"Erreur lors du téléchargement de {url}: {e}")
        return ""
    except trafilatura.utils.XMLSyntaxError as e:
        print(f"Erreur de syntaxe XML lors de l'extraction du texte: {e}")


def extract_sentences(text: str) -> List[str]:
    """
    Extrait toutes les phrases d'un texte.

    Args:
        text (str): Le texte à analyser

    Returns:
        List[str]: Liste des phrases extraites
    """
    if not text:
        return []

    # Nettoyer le texte
    text = text.strip()

    # Découper en phrases en utilisant des expressions régulières
    # Cette regex cherche les points, points d'exclamation, points d'interrogation
    # suivis d'un espace et d'une majuscule (ou fin de chaîne)
    sentences = re.split(r"(?<=[.!?])\s+(?=[A-Z])", text)

    # Nettoyer les phrases
    cleaned_sentences = []
    for sentence in sentences:
        sentence = sentence.strip()
        if sentence and len(sentence) > 3:  # Ignorer les phrases trop courtes
            cleaned_sentences.append(sentence)

    return cleaned_sentences


def save_to_csv(
    sentences: List[str], url: str, filename: str = "extracted_sentences.csv"
):
    """
    Sauvegarde les phrases extraites dans un fichier CSV.

    Args:
        sentences (List[str]): Liste des phrases à sauvegarder
        url (str): L'URL source
        filename (str): Nom du fichier CSV de sortie
    """
    try:
        with open(filename, "w", newline="", encoding="utf-8") as csvfile:
            writer = csv.writer(csvfile)

            # Écrire l'en-tête
            writer.writerow(["content", "url"])

            # Écrire les données
            for sentence in sentences:
                writer.writerow([sentence, url])

        print(f"Données sauvegardées dans {filename}")
        print(f"Nombre de phrases extraites: {len(sentences)}")

    except Exception as e:
        print(f"Erreur lors de la sauvegarde: {e}")


def process_url(url: str, output_file: str = "extracted_sentences.csv"):
    """
    Traite une URL complète: extraction du texte, des phrases et sauvegarde.

    Args:
        url (str): L'URL à traiter
        output_file (str): Nom du fichier de sortie
    """
    print(f"Traitement de l'URL: {url}")

    # Extraire le texte
    text = extract_text_from_html(url)

    if not text:
        print("Aucun texte extrait de la page.")
        return

    print(f"Texte extrait ({len(text)} caractères)")

    # Extraire les phrases
    sentences = extract_sentences(text)

    if not sentences:
        print("Aucune phrase extraite du texte.")
        return

    # Sauvegarder dans le CSV
    save_to_csv(sentences, url, output_file)


def process_multiple_urls(
    urls: List[str], output_file: str = "extracted_sentences.csv"
):
    """
    Traite plusieurs URLs et combine les résultats dans un seul fichier CSV.

    Args:
        urls (List[str]): Liste des URLs à traiter
        output_file (str): Nom du fichier de sortie
    """
    all_data = []

    for url in urls:
        print(f"\nTraitement de l'URL: {url}")

        # Extraire le texte
        text = extract_text_from_html(url)

        if not text:
            print("Aucun texte extrait de la page.")
            continue

        print(f"Texte extrait ({len(text)} caractères)")

        # Extraire les phrases
        sentences = extract_sentences(text)

        if not sentences:
            print("Aucune phrase extraite du texte.")
            continue

        # Ajouter aux données
        for sentence in sentences:
            all_data.append([sentence, url])

        print(f"Phrases extraites: {len(sentences)}")

    # Sauvegarder toutes les données
    if all_data:
        try:
            with open(output_file, "w", newline="", encoding="utf-8") as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(["content", "url"])
                writer.writerows(all_data)

            print(f"\nDonnées sauvegardées dans {output_file}")
            print(f"Total de phrases extraites: {len(all_data)}")

        except Exception as e:
            print(f"Erreur lors de la sauvegarde: {e}")
    else:
        print("Aucune donnée à sauvegarder.")


# Exemple d'utilisation
if __name__ == "__main__":
    # Exemple avec une seule URL
    url = "https://example.com"
    process_url(url, "sentences_example.csv")

    # Exemple avec plusieurs URLs
    urls = ["https://example.com", "https://another-example.com"]
    process_multiple_urls(urls, "sentences_multiple.csv")
