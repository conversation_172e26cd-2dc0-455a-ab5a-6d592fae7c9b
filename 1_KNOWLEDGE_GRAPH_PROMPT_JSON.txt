
https://github.com/di37/youtube-video-analysis-toolkit/blob/main/server/prompts/prompts.py#L6



# Knowledge Graph Generation Prompt

## Task
Extract entities and relationships from the provided text to create a knowledge graph in Neo4j style. Output the result as a JSON structure with nodes and relationships.

## Instructions

### 1. Node Extraction
Identify and extract entities as nodes with the following structure:
- **id**: Unique identifier (use snake_case)
- **label**: The type/category of the entity (e.g., Person, Organization, Location, Concept, Product, Event)
- **properties**: Dictionary of attributes (name, description, and any other relevant properties)

### 2. Relationship Extraction
Identify relationships between entities with the following structure:
- **id**: Unique identifier for the relationship
- **type**: The relationship type (use UPPERCASE_WITH_UNDERSCORES, e.g., WORKS_FOR, LOCATED_IN, CREATED_BY)
- **source**: The id of the source node
- **target**: The id of the target node
- **properties**: Dictionary of relationship attributes (optional)

### 3. Output Format
```json
{
  "nodes": [
    {
      "id": "node_id",
      "label": "NodeType",
      "properties": {
        "name": "Entity Name",
        "description": "Brief description",
        "other_property": "value"
      }
    }
  ],
  "relationships": [
    {
      "id": "rel_id",
      "type": "RELATIONSHIP_TYPE",
      "source": "source_node_id",
      "target": "target_node_id",
      "properties": {
        "property_name": "value"
      }
    }
  ]
}
```

## Entity Types to Consider
- **Person**: Individual people
- **Organization**: Companies, institutions, groups
- **Location**: Cities, countries, addresses, venues
- **Event**: Conferences, meetings, occurrences
- **Product**: Software, hardware, services
- **Concept**: Abstract ideas, technologies, methodologies
- **Document**: Reports, articles, publications
- **Date**: Specific dates or time periods

## Relationship Types to Consider
- **WORKS_FOR**: Person works for Organization
- **LOCATED_IN**: Entity is located in Location
- **CREATED_BY**: Product/Document created by Person/Organization
- **PARTICIPATES_IN**: Person participates in Event
- **RELATED_TO**: General relationship between concepts
- **MANAGES**: Person manages Person/Project
- **OWNS**: Organization owns Product/Asset
- **OCCURRED_ON**: Event occurred on Date

## Guidelines
1. Use consistent naming conventions (snake_case for IDs, PascalCase for labels)
2. Avoid duplicate nodes - reuse existing node IDs for the same entity
3. Include bidirectional relationships only when explicitly different (e.g., MANAGES vs IS_MANAGED_BY)
4. Extract only factual relationships explicitly stated or clearly implied in the text
5. Keep property values concise but informative
6. Ensure all relationship source and target IDs correspond to existing nodes

## Example

**Input Text:**
"John Smith, CEO of TechCorp, announced the launch of their new AI product called SmartAssist at the annual Tech Conference in San Francisco on March 15, 2024. The product was developed by the AI Research Team led by Dr. Sarah Johnson."

**Output:**
```json
{
  "nodes": [
    {
      "id": "john_smith",
      "label": "Person",
      "properties": {
        "name": "John Smith",
        "title": "CEO"
      }
    },
    {
      "id": "techcorp",
      "label": "Organization",
      "properties": {
        "name": "TechCorp",
        "type": "Company"
      }
    },
    {
      "id": "smartassist",
      "label": "Product",
      "properties": {
        "name": "SmartAssist",
        "type": "AI product"
      }
    },
    {
      "id": "tech_conference",
      "label": "Event",
      "properties": {
        "name": "Tech Conference",
        "type": "Annual conference"
      }
    },
    {
      "id": "san_francisco",
      "label": "Location",
      "properties": {
        "name": "San Francisco",
        "type": "City"
      }
    },
    {
      "id": "march_15_2024",
      "label": "Date",
      "properties": {
        "date": "2024-03-15"
      }
    },
    {
      "id": "ai_research_team",
      "label": "Organization",
      "properties": {
        "name": "AI Research Team",
        "type": "Team"
      }
    },
    {
      "id": "sarah_johnson",
      "label": "Person",
      "properties": {
        "name": "Dr. Sarah Johnson",
        "title": "Team Lead"
      }
    }
  ],
  "relationships": [
    {
      "id": "rel_1",
      "type": "WORKS_FOR",
      "source": "john_smith",
      "target": "techcorp",
      "properties": {
        "role": "CEO"
      }
    },
    {
      "id": "rel_2",
      "type": "ANNOUNCED",
      "source": "john_smith",
      "target": "smartassist",
      "properties": {}
    },
    {
      "id": "rel_3",
      "type": "CREATED_BY",
      "source": "smartassist",
      "target": "techcorp",
      "properties": {}
    },
    {
      "id": "rel_4",
      "type": "DEVELOPED_BY",
      "source": "smartassist",
      "target": "ai_research_team",
      "properties": {}
    },
    {
      "id": "rel_5",
      "type": "OCCURRED_AT",
      "source": "tech_conference",
      "target": "san_francisco",
      "properties": {}
    },
    {
      "id": "rel_6",
      "type": "OCCURRED_ON",
      "source": "tech_conference",
      "target": "march_15_2024",
      "properties": {}
    },
    {
      "id": "rel_7",
      "type": "LEADS",
      "source": "sarah_johnson",
      "target": "ai_research_team",
      "properties": {}
    },
    {
      "id": "rel_8",
      "type": "PART_OF",
      "source": "ai_research_team",
      "target": "techcorp",
      "properties": {}
    }
  ]
}
```

## Text to Analyze
[Aller au contenu](#content "Aller au contenu")

[![ZoneTuto](https://zonetuto.fr/wp-content/uploads/2022/12/logo-zone-tuto.png)](https://zonetuto.fr/)

Menu

Menu

- [Contact](https://zonetuto.fr/contact/)

## [Lexique pour comprendre le vocabulaire de l’intelligence artificielle](https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/)

12 juin 2025 par [MrTuto](https://zonetuto.fr/author/mrtuto/ "Afficher tous les articles de MrTuto")

[![](https://zonetuto.fr/wp-content/uploads/2025/06/ia-ai-intelligence-artificielle-glitch-femme-lexique-glossaire-mot-vocabulaire.jpg)](https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/)

Bienvenue dans mon guide exhaustif du vocabulaire de l’intelligence artificielle, l’IA. Ce domaine technologique, en pleine expansion, est porteur de transformations majeures dans de nombreux secteurs. Cependant, il s’accompagne d’un jargon spécifique qui peut parfois sembler complexe. L’objectif de ce lexique est de clarifier les termes et concepts clés de l’IA, afin de vous offrir … [Lire la suite](https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/ "Lexique pour comprendre le vocabulaire de l’intelligence artificielle")

Catégories [Intelligence artificielle](https://zonetuto.fr/intelligence-artificielle/) [Laisser un commentaire](https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/#respond)

## [Comment devenir riche dans GTA 5 avec la bourse](https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/)

12 juin 2025 par [MrTuto](https://zonetuto.fr/author/mrtuto/ "Afficher tous les articles de MrTuto")

[![](https://zonetuto.fr/wp-content/uploads/2025/06/gta-5-v-franklin-lester-riche-bourse-grand-theft-auto.jpg)](https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/)

Après un précédent article sur les choix à faire dans l’histoire de GTA 4, j’ai décidé de vous proposer un nouveau tutoriel. Ce tutoriel sur GTA IV a eu pas mal de succès donc je suis très content, je ne pensais pas que ce serait autant le cas pour un si vieux jeu sorti sur … [Lire la suite](https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/ "Comment devenir riche dans GTA 5 avec la bourse")

Catégories [Jeux vidéo](https://zonetuto.fr/jeux-video/) [Laisser un commentaire](https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/#respond)

## [Le gestionnaire de paquets WinGet sous Windows pour gérer les applications](https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/)

12 juin 202511 juin 2025 par [MrTuto](https://zonetuto.fr/author/mrtuto/ "Afficher tous les articles de MrTuto")

[![](https://zonetuto.fr/wp-content/uploads/2025/06/logo-windows-12-terminal-winget.jpg)](https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/)

Winget est le gestionnaire de paquets en ligne de commande officiel de Microsoft pour Windows. Il permet de rationaliser la manière dont les utilisateurs et les administrateurs interagissent avec les logiciels en centralisant leur gestion au sein d’un seul outil dans le terminal. Bien qu’il ne soit pas le premier outil de ce type, rejoignant … [Lire la suite](https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/ "Le gestionnaire de paquets WinGet sous Windows pour gérer les applications")

Catégories [Windows](https://zonetuto.fr/windows/) [Laisser un commentaire](https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/#respond)

## [Augmenter la taille de la fenêtre de contexte dans Ollama](https://zonetuto.fr/intelligence-artificielle/augmenter-la-taille-de-la-fenetre-de-contexte-dans-ollama/)

1 juin 2025 par [MrTuto](https://zonetuto.fr/author/mrtuto/ "Afficher tous les articles de MrTuto")

[![](https://zonetuto.fr/wp-content/uploads/2025/06/llama-ollama-llm-ia-picto-vector-illustration.jpg)](https://zonetuto.fr/intelligence-artificielle/augmenter-la-taille-de-la-fenetre-de-contexte-dans-ollama/)

Ollama est vraiment un outil formidable qui vous permet d’utiliser de nombreux modèles d’intelligence artificielle en local très facilement. Je vous ai récemment écrit un tutoriel pour démarrer facilement avec Ollama et l’IA en local. Même si votre machine n’est pas très puissante, avec un processeur pas trop ancien et un peu de RAM, au … [Lire la suite](https://zonetuto.fr/intelligence-artificielle/augmenter-la-taille-de-la-fenetre-de-contexte-dans-ollama/ "Augmenter la taille de la fenêtre de contexte dans Ollama")

Catégories [Intelligence artificielle](https://zonetuto.fr/intelligence-artificielle/) [Laisser un commentaire](https://zonetuto.fr/intelligence-artificielle/augmenter-la-taille-de-la-fenetre-de-contexte-dans-ollama/#respond)

## [Windows : impossible d’ouvrir des fichiers avec un nom trop long](https://zonetuto.fr/windows/impossible-ouvrir-fichiers-avec-un-nom-trop-long/)

22 mai 2025 par [MrTuto](https://zonetuto.fr/author/mrtuto/ "Afficher tous les articles de MrTuto")

[![](https://zonetuto.fr/wp-content/uploads/2025/05/windows-logo-glitch.jpg)](https://zonetuto.fr/windows/impossible-ouvrir-fichiers-avec-un-nom-trop-long/)

Récemment, plusieurs utilisateurs ont signalé des difficultés à ouvrir des documents Microsoft Word directement depuis nos lecteurs réseau mappés. Il faut savoir que les utilisateurs sont des êtres facétieux qui aiment bien faire des blagues à la DSI. Comme vous le savez sûrement déjà, il ne faut jamais faire confiance aux utilisateurs. Bon ok, j’ai … [Lire la suite](https://zonetuto.fr/windows/impossible-ouvrir-fichiers-avec-un-nom-trop-long/ "Windows : impossible d’ouvrir des fichiers avec un nom trop long")

Catégories [Windows](https://zonetuto.fr/windows/) [Laisser un commentaire](https://zonetuto.fr/windows/impossible-ouvrir-fichiers-avec-un-nom-trop-long/#respond)

## [Stack Overflow est-il en train de tirer sa révérence ?](https://zonetuto.fr/outils/stack-overflow-tirer-reverence/)

18 mai 2025 par [ConceptEure](https://zonetuto.fr/author/concepteure/ "Afficher tous les articles de ConceptEure")

[![Déclin de StackOverflow par année](https://zonetuto.fr/wp-content/uploads/2025/05/stackoverflow_questions-1-scaled.png)](https://zonetuto.fr/outils/stack-overflow-tirer-reverence/)

Ah, Stack Overflow… Ce nom qui résonne dans la tête de millions de développeurs à travers le monde. Pendant des années, ce fut « la bouée » de sauvetage, le phare dans la nuit pour quiconque se heurtait à un bug récalcitrant ou à une énigme de code insoluble. Mais voilà, depuis quelque temps, une rumeur enfle, … [Lire la suite](https://zonetuto.fr/outils/stack-overflow-tirer-reverence/ "Stack Overflow est-il en train de tirer sa révérence ?")

Catégories [Intelligence artificielle](https://zonetuto.fr/intelligence-artificielle/), [Outils](https://zonetuto.fr/outils/) [Laisser un commentaire](https://zonetuto.fr/outils/stack-overflow-tirer-reverence/#respond)

## [Lister les fichiers et leur chemin complet sur un lecteur Windows](https://zonetuto.fr/windows/lister-les-fichiers-et-leur-chemin-complet-sur-un-lecteur-windows/)

18 mai 2025 par [MrTuto](https://zonetuto.fr/author/mrtuto/ "Afficher tous les articles de MrTuto")

[![](https://zonetuto.fr/wp-content/uploads/2025/05/disque-dur-hardisk-vector-illustration-icon.jpg)](https://zonetuto.fr/windows/lister-les-fichiers-et-leur-chemin-complet-sur-un-lecteur-windows/)

Dans un précédent article, je vous ai expliqué comment obtenir la liste des lecteurs réseaux et locaux connectés à votre Windows. Cette introduction était nécessaire pour avoir l’ensemble des lettres des lecteurs. Aujourd’hui, nous allons nous servir de cette fameuse lettre, car il s’agit du point de départ de notre recherche. En effet, nous allons … [Lire la suite](https://zonetuto.fr/windows/lister-les-fichiers-et-leur-chemin-complet-sur-un-lecteur-windows/ "Lister les fichiers et leur chemin complet sur un lecteur Windows")

Catégories [Windows](https://zonetuto.fr/windows/) [Laisser un commentaire](https://zonetuto.fr/windows/lister-les-fichiers-et-leur-chemin-complet-sur-un-lecteur-windows/#respond)

## [Ajouter et exécuter des LLM avec Ollama pour utiliser l’IA en local](https://zonetuto.fr/intelligence-artificielle/ajouter-et-executer-des-llm-avec-ollama-pour-utiliser-lia-en-local/)

16 mai 2025 par [MrTuto](https://zonetuto.fr/author/mrtuto/ "Afficher tous les articles de MrTuto")

[![](https://zonetuto.fr/wp-content/uploads/2025/05/ollama-llama-ia-local-logiciel-llm-logo.jpg)](https://zonetuto.fr/intelligence-artificielle/ajouter-et-executer-des-llm-avec-ollama-pour-utiliser-lia-en-local/)

L’intelligence artificielle, et plus particulièrement les grands modèles de langage (LLM), transforment notre façon de travailler, de créer et d’interagir. De ChatGPT à Claude d’Anthropic en passant par Mistral, les possibilités semblent infinies et de nouvelles entreprises se lancent continuellement. Mais une barrière se dresse souvent : la dépendance aux API cloud, avec leurs coûts, … [Lire la suite](https://zonetuto.fr/intelligence-artificielle/ajouter-et-executer-des-llm-avec-ollama-pour-utiliser-lia-en-local/ "Ajouter et exécuter des LLM avec Ollama pour utiliser l’IA en local")

Catégories [Intelligence artificielle](https://zonetuto.fr/intelligence-artificielle/) [Laisser un commentaire](https://zonetuto.fr/intelligence-artificielle/ajouter-et-executer-des-llm-avec-ollama-pour-utiliser-lia-en-local/#respond)

## [Lister les lecteurs réseaux et vérifier leur espace disque dans Windows](https://zonetuto.fr/windows/lister-les-lecteurs-reseaux-mappes-et-verifier-leur-espace-disque/)

22 mai 20256 mai 2025 par [MrTuto](https://zonetuto.fr/author/mrtuto/ "Afficher tous les articles de MrTuto")

[![](https://zonetuto.fr/wp-content/uploads/2025/05/disque-dur-ssd-lecteur-reseau-mappe-windows.jpg)](https://zonetuto.fr/windows/lister-les-lecteurs-reseaux-mappes-et-verifier-leur-espace-disque/)

En environnement professionnel, les lecteurs réseau constituent un accès essentiel aux ressources partagées sur les serveurs internes. Que votre rôle soit celui d’administrateur système, de technicien support, ou que vous soyez simplement un utilisateur averti, savoir identifier ces connexions et surveiller leur espace disque est fondamental pour une gestion efficiente. Cet article vous guide pas … [Lire la suite](https://zonetuto.fr/windows/lister-les-lecteurs-reseaux-mappes-et-verifier-leur-espace-disque/ "Lister les lecteurs réseaux et vérifier leur espace disque dans Windows")

Catégories [Windows](https://zonetuto.fr/windows/) [Laisser un commentaire](https://zonetuto.fr/windows/lister-les-lecteurs-reseaux-mappes-et-verifier-leur-espace-disque/#respond)

## [Classement des meilleurs modèles IA pour créer des images](https://zonetuto.fr/intelligence-artificielle/classement-des-meilleurs-modeles-ia-pour-creer-des-images/)

24 avril 2025 par [MrTuto](https://zonetuto.fr/author/mrtuto/ "Afficher tous les articles de MrTuto")

[![](https://zonetuto.fr/wp-content/uploads/2025/04/evaluation-test-llm-text-to-image-leaderboard-classement.jpeg)](https://zonetuto.fr/intelligence-artificielle/classement-des-meilleurs-modeles-ia-pour-creer-des-images/)

Avec la profusion de nouveautés en matière d’intelligence artificielle, ce n’est pas forcément facile de s’y retrouver. Chaque jour, il y a des nouveaux modèles qui sortent pour générer du texte, des images, des vidéos, de la musique et etc … L’actualité autour de l’IA est très riche et cela peut parfois devenir difficile de … [Lire la suite](https://zonetuto.fr/intelligence-artificielle/classement-des-meilleurs-modeles-ia-pour-creer-des-images/ "Classement des meilleurs modèles IA pour créer des images")

Catégories [Intelligence artificielle](https://zonetuto.fr/intelligence-artificielle/) [Laisser un commentaire](https://zonetuto.fr/intelligence-artificielle/classement-des-meilleurs-modeles-ia-pour-creer-des-images/#respond)

[Articles plus anciens](https://zonetuto.fr/page/2/)

Page1 [Page2](https://zonetuto.fr/page/2/) … [Page25](https://zonetuto.fr/page/25/) [→ suivant](https://zonetuto.fr/page/2/)

Rechercher

Rechercher

- [Lexique pour comprendre le vocabulaire de l’intelligence artificielle](https://zonetuto.fr/intelligence-artificielle/lexique-glossaire-comprendre-vocabulaire-ia/)
- [Comment devenir riche dans GTA 5 avec la bourse](https://zonetuto.fr/jeux-video/comment-devenir-riche-dans-gta-5-avec-la-bourse/)
- [Le gestionnaire de paquets WinGet sous Windows pour gérer les applications](https://zonetuto.fr/windows/gestionnaire-de-paquets-winget-gerer-applications/)
- [Augmenter la taille de la fenêtre de contexte dans Ollama](https://zonetuto.fr/intelligence-artificielle/augmenter-la-taille-de-la-fenetre-de-contexte-dans-ollama/)
- [Windows : impossible d’ouvrir des fichiers avec un nom trop long](https://zonetuto.fr/windows/impossible-ouvrir-fichiers-avec-un-nom-trop-long/)

<!--THE END-->

- [Astuce](https://zonetuto.fr/astuce/)
- [Fichier 3D STL](https://zonetuto.fr/fichier-3d-stl/)
- [Hardware](https://zonetuto.fr/hardware/)
- [Imprimante 3D](https://zonetuto.fr/imprimante-3d/)
- [Intelligence artificielle](https://zonetuto.fr/intelligence-artificielle/)
- [JavaScript](https://zonetuto.fr/javascript/)
- [Jeux vidéo](https://zonetuto.fr/jeux-video/)
- [Linux](https://zonetuto.fr/linux/)
- [macOS](https://zonetuto.fr/macos/)
- [NodeJS](https://zonetuto.fr/nodejs/)
- [Outils](https://zonetuto.fr/outils/)
- [PHP](https://zonetuto.fr/php/)
- [Python](https://zonetuto.fr/python/)
- [Shell BASH](https://zonetuto.fr/shell-bash/)
- [Windows](https://zonetuto.fr/windows/)
- [WordPress](https://zonetuto.fr/wordpress/)

<!--THE END-->

1. Bebert sur [Firefox Snap : comment faire la mise à jour du navigateur sur Ubuntu](https://zonetuto.fr/linux/firefox-snap-comment-faire-la-mise-a-jour-du-navigateur-sur-ubuntu/#comment-11931)
2. Bonaventure Claude sur [Filament PLA vs PETG : quelle différence ? Qui est le meilleur ?](https://zonetuto.fr/imprimante-3d/filament-pla-vs-petg-quelle-difference-qui-est-le-meilleur/#comment-11391)
3. Mathieu sur [Ajouter les bonnes permissions sur les fichiers et dossiers de WordPress](https://zonetuto.fr/linux/ajouter-les-bonnes-permissions-sur-les-fichiers-et-dossiers-de-wordpress/#comment-10346)
4. Jak sur [Écran noir sur Ubuntu 24.04 LTS avec une carte graphique Nvidia](https://zonetuto.fr/hardware/ecran-noir-sur-ubuntu-24-04-lts-avec-une-carte-graphique-nvidia/#comment-9928)
5. Marc sur [Déployer un projet hébergé dans Gitlab sur Ubuntu avec clé SSH et Git](https://zonetuto.fr/linux/deployer-un-projet-heberge-dans-gitlab-sur-ubuntu-avec-cle-ssh-et-git/#comment-9905)

<!--THE END-->

- [Contact](https://zonetuto.fr/contact/)
- [Mentions légales](https://zonetuto.fr/mentions-legales/)

© 2025 ZoneTuto • Construit avec [GeneratePress](https://generatepress.com)